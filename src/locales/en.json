{"bc": {"certificate-store": "Certificate Store", "roles": "Roles", "license-notifications": "License Notifications", "audits": "Audits", "emergency-notifications": "Emergency notifications", "customers": "Customers", "billing": "Billing", "locations": "Locations", "notifications": "Notifications", "sales-analysis": "Sales analysis", "software-licenses": "Software Licenses", "settings": "Settings", "support": "Support", "cloudspaces": "Cloudspaces", "objectspaces": "Objectspaces", "containerspaces": "Containerspaces (beta)", "disks": "Disks", "vgpus": "vGPUs", "recycle-bin": "Recycle bin", "spendings": "Spendings", "docs": "Docs", "iam": "IAM", "credit-cards": "Credit cards", "cloud-admin": "Cloud admin", "admin": "Admin", "license-compliance": "License compliance"}, "admin": {"selected": "selected", "policy-0": "policy", "policies-0": "policies", "retry": "Retry", "delete-selected-targets": "Delete selected targets", "delete-selected-policies": "Delete selected policies", "previous-backup": "Previous Backup", "vm-id": "VM Id", "duration-minutes": "Duration (minutes)", "policy": "Policy", "data-size-gib": "Data size (GiB)", "snapshot-time": "Snapshot time", "snapshot_duration": "Snapshot duration (minutes)", "please-confirm-deleting-policy-item-name": "Please confirm deleting policy: {0}", "snapshot-policy-details": "Snapshot Policy Details", "policy-name": "Policy Name *", "number-of-retries-before-giving-up-on-a-failed-snapshot": "Number of retries before giving up on a failed snapshot", "time-between-retry-attempts-after-a-failure": "Time between retry attempts after a failure", "maximum-time-allowed-for-a-snapshot-to-complete": "Maximum time allowed for a snapshot to complete", "action-to-take-if-a-cooperative-snapshot-fails-e-g-retry-abort": "Action to take if a cooperative snapshot fails (e.g., retry, abort)", "time-to-wait-for-other-cooperative-processes-before-timing-out": "Time to wait for other cooperative processes before timing out", "enables-multiple-snapshot-operations-to-run-without-conflict": "Enables multiple snapshot operations to run without conflict", "how-long-a-backup-lock-remains-before-being-released": "how long a backup lock remains before being released", "how-long-backups-are-kept-based-on-count-or-time-e-g-last-n-snapshots-hourly-daily-or-within-a-set-duration": "how long backups are kept based on count or time (e.g., last N snapshots, hourly, daily, or within a set duration)", "schedule-in-cron-format": "Schedule in cron format", "name-of-the-backup-target": "Name of the backup target", "restics-lock-handling-exclusive-shared-none": "Restic's lock handling (exclusive, shared, none)", "name-of-the-storage-bucket": "Name of the storage bucket", "s3-bucket-region-e-g-us-east-1": "S3 bucket region (e.g., us-east-1)", "s3-endpoint": "S3 endpoint", "policy-name-updated-successfully": "policy name updated successfully", "retry-times-updated-successfully": "Retry times updated successfully", "retry-pause-updated-successfully": "Retry pause updated successfully", "snapshot-timeout-updated-successfully": "Snapshot timeout updated successfully", "failure-behavior-updated-successfully": "Failure behavior updated successfully", "timeout-updated-successfully": "timeout updated successfully", "failure-report-email-updated-successfully": "Failure report email updated successfully", "add-target": "Add Target", "restic-password": "Restic password *", "basic-information": "Basic information", "bucket-name": "Bucket name *", "secret-key": "Secret key *", "access-key": "Access key *", "s3-details": "S3 details", "no-available-data": "No available data", "loading-cloudspaces": "loading cloudspaces", "select-cloudspace-to-use-its-network-to-access-target": "Select cloudspace", "basic-information-required": "Basic information (required)", "s3-information-required": "S3 information (required)", "select-cloudspace": "Select Cloudspace", "target-details": "Target Details", "backup-targets": "Backup Targets", "s3-secret-key": "S3 Secret Key", "s3-access-key": "S3 Access Key", "s3-locking-mode-0": "S3 Locking Mode", "s3-bucket-0": "S3 Bucket", "s3-locking-mode-updated-successfully": "S3 Locking Mode updated successfully", "s3-locking-mode": "S3 Locking Mode", "s3-bucket-updated-successfully": "S3 Bucket updated successfully", "s3-bucket": "S3 Bucket", "s3-region-updated-successfully": "S3 Region updated successfully", "s3-region": "S3 Region", "s3-url-updated-successfully": "S3 URL updated successfully", "s3-url": "S3 URL", "restic-password-updated-successfully": "Restic Password updated successfully", "restic-password-0": "Restic Password", "cloudspace-id-updated-successfully": "Cloudspace id updated successfully", "please-confirm-deleting-selected-policies": "Please confirm deleting selected policies", "backup-target-is-set-as-default-successfully": "Backup target is set as default successfully!", "backup-policies-are-deleted-successfully": "Backup policies are deleted successfully!", "backup-targets-are-deleted-successfully": "Backup targets are deleted successfully!", "backup-target-is-deleted-successfully": "Backup target was deleted successfully!", "please-confirm-deleting-selected-targets": "Please confirm deleting selected targets", "please-confirm-deleting-target-item-name": "Please confirm deleting target {0}", "please-confirm-deleting-backup-item-name": "Please confirm deleting backup {0}", "delete-backup-target-0": "Delete backup target", "delete-backup-targets": "Delete backup targets", "delete-backup-policies": "Delete backup policies", "cloudspace-id": "Cloudspace ID", "set-backup-target-as-default": "Set backup target as default", "delete-backup-target": "Delete backup target", "delete-backup": "Delete backup", "click-to-copy-password": "Click to copy password", "backup-policy-is-set-as-default-successfully": "Backup policy is set as default successfully!", "set-as-default": "Set as default", "backup-policy-is-deleted-successfully": "Backup policy was deleted successfully!", "delete-backup-policy-0": "Delete backup policy", "retention-duration": "Retention duration", "retention-flags": "Retention flags", "set-as-default-policy": "Set as default policy", "delete-backup-policy": "Delete backup policy", "add-backup-target": "Add Backup Target", "add-backup-policy": "Add Backup Policy", "assign-backup-policy": "Assign Backup Policy", "policy-updated-successfully": "Policy updated successfully", "policy-details": "Policy Details", "backup-policies": "Backup Policies", "backup": "Backup", "subscribed-locations": "Subscribed locations", "snapshot-policy-retry-pause": "Retry pause", "snapshot-policy-timeout": "Policy timeout", "snapshot-failure-behaviour": "Failure behavior", "snapshot-policy-cooperative-timeout": "Cooperative timeout", "failure-report-email-0": "Failure report email", "snapshot-retry-times": "Retry Times", "snapshot-retry-pause": "<PERSON><PERSON> Pause", "snapshot-timeout": "Timeout", "snapshot-cooperative-failure-behaviour": "Cooperative Failure Behaviour", "snapshot-cooperative-timeout": "Cooperative Timeout", "snapshot-cooperative": "Cooperative", "locking-retention-duration-updated-successfully": "locking retention duration updated successfully", "restic-retention-flags-updated-successfully": "restic retention flags updated successfully", "snapshot-policy": "Snapshot Policy", "restic-retention-flags-0": "Restic Retention Flags", "cron-updated-successfully": "Cron updated successfully", "cron": "<PERSON><PERSON>", "backup-target-updated-successfully": "Backup Target updated successfully", "this-a-record-already-exists-for-this-domain-if-several-hosts-are-configured-with-the-same-domain-round-robin-scheduling-will-be-applied-to-distribute-incoming-requests-for-this-domain-among-the-hosts-are-you-sure-you-want-to-proceed": "This A record already exists for this domain. If several hosts are configured with the same domain, Round Robin scheduling will be applied to distribute incoming requests for this domain among the hosts. Are you sure you want to proceed?", "backup-policy-is-created-successfully": "Backup policy was created successfully!", "add-policy": "Add policy", "target": "Target", "failure-report-email": "Failure report email  *", "locking-retention-duration": "Locking retention duration", "locking-retention-duration-0": "Locking retention duration", "restic-retention-flags": "Restic retention flags", "frequency-0": "Frequency *", "and-retention-policy": "& retention policy", "configure-frequency": "Configure frequency", "retry-pause": "Retry pause *", "retry-times": "Retry times *", "locking-mode": "Locking mode *", "timeout": "Timeout *", "cooperative-timeout": "cooperative timeout *", "cooperative-if-set-it-will-freeze-the-vms-file-systems-during-the-snapshot-operation": "cooperative (If set, it will freeze the VM's file systems during the snapshot operation)", "snapshot-policy-configuration": "Snapshot policy configuration", "select-backup-target": "Select backup target", "and-retention-policy-required": "& retention policy (required)", "frequency": "Frequency", "configure-snapshot-policy-required": "Configure snapshot policy (required)", "select-target-required": "Select target (required)", "backups": "Backups", "targets": "Targets", "policies": "Policies", "require-vm-backup-policy-note": "When enabled, VMs created by customers must have a backup policy", "allow-override-note": "Allow customer admins to define and modify backup targets and/or policies", "require-backup-on-vm": "Require backup on VM", "default-policy": "Default Policy", "default-target": "Default target", "loading-locations": "Loading locations", "no-locations-available": "No locations available", "selected-invoices-are-deleted-successfully": "Selected invoices are deleted successfully!", "please-confirm-deleting-selected-invoices": "Please confirm deleting selected invoices", "delete-invoices": "Delete invoices", "re-generate-invoices": "(Re)generate Invoices", "delete-selected-invoices": "Delete selected invoices", "invoices": "Invoices", "admin": "Admin", "december": "December", "november": "November", "october": "October", "september": "September", "august": "August", "july": "July", "june": "June", "may": "May", "april": "April", "march": "March", "february": "February", "january": "January", "payment-status": "Payment status", "status": "Status", "amount": "Amount", "month": "Month", "created-on": "Created on", "customer": "Customer", "invoice-number": "Invoice number", "invoice-is-paid-successfully": "Invoice is paid successfully!", "please-confirm-paying-invoice-with-total-value-this-data-total_incl-this-currencysymbol-this-data-currency-using-credit-card-this-cardmaskedpan": "Please confirm paying invoice with total value {0} {1} using credit card {2} :", "invoice-is-deleted-successfully": "Invoice is deleted successfully!", "please-confirm-deleting-invoice-with-id-this-invoiceid": "Please confirm deleting invoice with id {0} :", "delete-invoice": "Delete invoice", "set-prices-for-licenses-of-type": "Set prices for licenses of type", "invoice-is-re-generated-successfully": "Invoice is (re)generated successfully!", "are-you-sure-you-want-to-re-generate-invoice": "Are you sure you want to (re)generate invoice ?", "re-generate-invoice": "(Re)generate invoice", "invoice-is-successfully-sent-to-this-data-customer_id": "Invoice is successfully sent to {0}!", "are-you-sure-you-want-to-resend-invoice-to-customer-this-data-customer_id": "Are you sure you want to resend invoice to customer {0} ?", "resend-invoice": "Resend invoice", "invoice-details-is-successfully-unset": "Invoice details is successfully unset!", "are-you-sure-you-want-to-unset-invoice": "Are you sure you want to unset invoice ?", "unset-invoice-pdf": "Unset invoice pdf", "not": "Not", "pay-with-card": "Pay with card", "resend": "Resend", "send": "Send", "draft": "Draft", "sent": "<PERSON><PERSON>", "mark-as": "Mark as ...", "edit-extra-charges": "Edit extra charges", "save-and-regenerate-invoice": "Save and regenerate invoice", "actions": "Actions", "expiry": "Expiry", "monthly-reoccurring": "Monthly reoccurring", "quantity": "Quantity", "unit-price-this-fromcurrency": "Unit price {0}", "description": "Description", "paid": "Paid", "set-prices": "Set prices", "save": "Save", "close": "Close", "invoice-status": "Invoice status:", "change-invoice-payment-status": "Change invoice payment status", "send-invoice": "Send invoice", "download": "Download", "delete": "Delete", "invoice-document": "Invoice Document", "never": "Never", "add": "Add", "when-adding-a-charge-to-an-invoice-you-can-indicate-that-the-extra-charge-is-monthly-recurring-optionally-you-can-also-indicate-until-when-the-monthly-recurring-extra-charge-needs-to-applied": "When adding a charge to an invoice, you can indicate that the extra charge is monthly recurring. Optionally you can also indicate until when the monthly recurring extra charge needs to applied.", "the-xtr-unit-does-not-have-a-standard-price-when-adding-an-extra-charge-to-an-invoice-you-have-to-determine-the-amount-and-price-the-price-also-can-be-negative-discount-charge": "The XTR unit does not have a standard price. When adding an extra charge to an invoice, You have to determine the amount and price. The price also can be negative (discount charge).", "extra-charges": "Extra charges", "total-including-vat": "Total including VAT", "data-vat_pct": "{0} %", "total-excluding-vat": "Total excluding VAT", "payment-time": "Payment time", "sent-on": "<PERSON>t on", "billing-month": "Billing month", "creation-date": "Creation date", "invoice-will-be-generated-in-your-standard-currency-tocurrency-invoices-for-the-vcos-that-have-prices-set-in-other-currencies-will-be-recalculated-in-tocurrency-price-in-the-original-vco-currency-will-be-reflected-in-the-invoice-for-information-purpose-as-display-price": "Invoice will be generated in your standard currency {0}. Invoices for the VCOs that have prices set in other currencies will be recalculated in {1}. Price in the original VCO currency will be reflected in the invoice for information purpose as *display price*", "exchange-rates": "Exchange rates", "for-customer": "for customer:", "invoices-are-generated-successfully-for-this-months-new-date-this-timestamp-1000-getmonth-new-date-this-timestamp-1000-getfullyear": "Invoices are generated successfully for {0} / {1}", "regenerate": "Regenerate", "all-invoices-will-be-generated-in-your-standard-currency-vcocurrency-invoices-for-the-customers-that-have-prices-set-in-other-currencies-will-be-recalculated-in-vcocurrency-price-in-the-original-customer-currency-will-be-reflected-in-the-invoice-for-information-purpose-as-display-price": "All invoices will be generated in your standard currency {0}. Invoices for the customers that have prices set in other currencies will be recalculated in {1}. Price in the original customer currency will be reflected in the invoice for information purpose as *display price*", "cancel": "Cancel", "eg-february-2021": "EG: February / 2021", "select-a-month-you-want-to-re-generate-invoices-for": "Select a month you want to (re)generate invoices for:", "location": "Location", "price": "Price", "part-number": "Part number", "microsoft-licenses-prices": "Microsoft Licenses prices:", "locations-pricing": "Locations pricing", "locations": "Locations", "you-can-enable-custom-pricing-for-a-specific-customer-in-the-customer-details-page": "You can enable custom pricing for a specific customer in the customer details page.", "screen-allows-changing-standard-prices-used-by-default-for-all-customers": "screen allows changing standard prices used, by default, for all customers.", "selling": "Selling", "screen-shows-the-cloud-enabler-prices-for-this-virtual-cloud-operator": "screen shows the cloud enabler prices for this virtual cloud operator.", "buying": "Buying", "the": "The", "pricing-management": "Pricing Management", "prices-updated-successfully": "Prices updated successfully", "discard-changes": "Discard changes", "edit": "Edit", "convert": "Convert", "proceeed-without-converting": "Pro<PERSON>eed without converting", "conversion-rate-1-convertedcurrency-oldval": "Conversion rate 1 {0} =", "conver-prices-from-convertedcurrency-oldval-to-convertedcurrency-newval": "Conver prices from {0} to {1}?", "back": "Back", "notifications": "Notifications", "service-impact-enumtotext-blog-service_impact": "Service Impact: {0}", "maintenance-status-enumtotext-blog-maintenance_status": "Maintenance status: {0}", "reason-blog-reason": "Reason: {0}", "till-timestamptodate-blog-till_time": "Till: {0}", "from-timestamptodate-blog-from_time": "From: {0}", "blog-preview": "Blog preview", "notification-sent-successfully": "Notification sent successfully", "send-notification": "Send notification", "this-notification-is-already-published-and-sent-to-its-recipients-send-again-to-all-subscribed-users": "This notification is already published and sent to its recipients. Send again to all subscribed users?", "publish-this-notification-and-send-it-to-all-subscribed-users": "Publish this notification and send it to all subscribed users?", "notification-deleted-successfully": "Notification deleted successfully", "please-confirm-deleting-notification-item-title": "Please confirm deleting notification {0}", "delete-notification": "Delete Notification", "new-notification": "New Notification", "sender": "Sender", "type": "Type", "created": "Created", "title": "Title", "create-a-copy": "Create a copy", "include-past": "Include past", "notification-content": "Notification content", "creating-notifications": "Creating notifications", "add-email": "Add email", "send-the-email-notification-to-the-following-addresses": "Send the email notification to the following addresses", "test-email-notification": "Test email notification", "till": "<PERSON>", "from": "From", "notification-reason": "Notification reason", "reason": "Reason *", "maintenance-status": "Maintenance Status", "service-impact": "Service Impact", "notification-type": "Notification type *", "notification-title": "Notification title", "blog-status": "Blog status:", "notification-status": "Notification status:", "the-notification-status": ": the notification status", "the-notification-impact": ": the notification impact", "the-notification-reason": ": the notification reason", "city-location-name": "city (location name)", "the-affected-locations-as": ": the affected locations as", "the-notification-end-time-ex": ": the notification end time ex.", "wednesday-22nd-of-march-10-39-am-utc-time": "Wednesday 22nd of March 10:39 AM UTC time", "the-notification-start-time-ex": ": the notification start time ex.", "wednesday-22nd-of-march": "Wednesday 22nd of March", "the-notification-start-date-ex": ": the notification start date ex.", "variables-limited-to-planned-maintenance-and-outage-warnings": "Variables limited to Planned maintenance and outage warnings", "the-vco-logo": ": the vco logo", "and-can-be-used-while-adding-links": "and can be used while adding links", "the-vco-link-as": ": the vco link as", "the-vco-name": ": the vco name", "variables-available-in-the-notification-content-that-will-be-rendered-on-notification-sending-blog-publishing-and-blog-updating": "Variables available in the notification content that will be rendered on notification sending, blog publishing and blog updating:", "total-size-of-images-attached-to-the-notification-content-is-limited-to-1mb": "Total size of images attached to the notification content is limited to 1MB", "can-not-be-updated-or-deleted": "can not be updated or deleted", "notifications-in-status": "Notifications in status", "vcotimezone": "({0})", "utc-dates-in-the-content-will-be-replaced-with-hostname-timezone": "UTC dates in the content will be replaced with {0} timezone", "when-sending-planned-maintenance-notifications": "When sending planned maintenance notifications,", "placeholders-in-the-content-will-be-replaced-with-dates-in-utc-timezone": "placeholders in the content will be replaced with dates in UTC timezone", "when-creating-a-new-planned-maintenance-notification": "When creating a new planned maintenance notification,", "and": "and", "send-preview": "Send preview", "copy": "Copy", "create-blog": "Create blog", "unpublish-blog": "Unpublish blog", "notification-successfully-updated": "Notification successfully updated", "images-attached-to-the-notification-are-too-large-limit-is-1mb": "Images attached to the notification are too large, Limit is 1MB", "notification-successfully-copied": "Notification successfully copied", "notification-successfully-created": "Notification successfully created", "please-confirm-deleting-notification": "Please confirm deleting notification", "delete-notification-blog": "Delete notification blog", "please-confirm-deleting-notification-blog": "Please confirm deleting notification blog", "notification-blog-deleted-successfully": "Notification blog deleted successfully", "test-notification-is-sent-to-your-emails": "Test notification is sent to your emails", "notification-already-sent": "Notification already sent", "please-confirm-publishing-this-blog": "Please confirm Publishing this blog", "updating-notification": "Updating notification", "blog-created-successfully": "Blog created successfully", "blog-unpublished-successfully": "Blog unpublished successfully", "audit-forwarding-configuration-is-deleted-successfully": "Audit forwarding configuration is deleted successfully!", "please-confirm-removing-audit-logs-forwarding-configuration": "Please confirm removing audit logs forwarding configuration", "delete-audit-logs-forwarding-configuration": "Delete audit logs forwarding configuration", "audit-log-forwarding-is-set-successfully": "Audit log forwarding is set successfully!", "audit-logs-forwarding-is-not-set": "Audit logs forwarding is not set", "status-of-the-last-forwarded-audit": "Status of the last forwarded audit", "value": "Value", "key": "Key", "add-header": "Add header", "headers": "Headers", "url": "Url", "once-configuration-is-complete-the-portal-will-start-continuously-sending-audits-of-all-post-put-delete-requests-to-your-external-system-in-json-format": "Once configuration is complete, the portal will start continuously sending audits of all POST/PUT/DELETE requests to your external system in JSON format.", "add-the-headers-required-for-your-post-call-for-example-authorization-header": "Add the headers required for your POST call, for example authorization header.", "add-the-url-of-an-external-service-where-you-want-to-post-audits-of-the-portal-in-real-time": "Add the URL of an external service where you want to POST audits of the portal in real time.", "upload-audits-to-external-system": "Upload audits to external system", "branding-colors-err-message": "Branding colors: {0}", "service-is-offline": "Service is offline", "portal-live-chat": "Portal Live Chat", "please-note-that-updating-the-utility-name-will-result-in-changing-the-name-of-distributed-binaries-e-g-cli-terraform-provider-existing-scripts-using-these-binaries-will-need-to-be-adapted-accordingly": "Please note that updating the utility name will result in changing the name of distributed binaries (e.g. cli, terraform provider). Existing scripts using these binaries will need to be adapted accordingly.", "please-note-that-updating-portal-name-will-be-reflected-in-the-portal-and-in-the-vco-documentation": "Please note that updating portal name will be reflected in the portal and in the VCO documentation.", "please-note-that-updating-the-utility-name-will-result-in-changing-the-name-of-distributed-binaries-e-g-cli-terraform-provider-existing-scripts-using-these-binaries-will-need-to-be-adapted-accordingly-updating-portal-name-will-be-reflected-in-the-portal-and-in-the-vco-documentation": "Please note that updating the utility name will result in changing the name of distributed binaries (e.g. cli, terraform provider). Existing scripts using these binaries will need to be adapted accordingly. Updating portal name will be reflected in the portal and in the VCO documentation.", "warning": "Warning", "update-vco-logo": "Update VCO logo", "portal-description": "Portal description", "utility-name": "Utility name", "portal-name": "Portal name", "website": "Website", "vco-website-logout-redirection": "VCO website (Logout redirection):", "secondary-color": "Secondary color:", "hex-color": "Hex color", "rgb-color": "RGB color", "primary-color": "Primary color:", "configure": "Configure", "settings": "Settings", "domain-ns-configuration-not-validated": "Domain NS configuration not validated", "domain-ns-configuration-validated": "Domain NS configuration validated", "domain-not-verified": "Domain not verified", "verified": "Verified", "domain-name": "Domain name", "follow-the-instructions-to-add-these-ns-records-in-your-dns-configuration": "Follow the instructions to add these NS records in your DNS configuration:", "2-add-ns-record": "2- Add NS record", "note-it-may-take-up-to-72-hours-for-the-change-to-propagate-across-your-servers": "Note: It may take up to 72 hours for the change to propagate across your servers.", "3-click-verify-domain": "3- Click Verify domain.", "follow-the-instructions-to-add-this-txt-record-in-your-dns-configuration-domainverificationcode": "Follow the instructions to add this TXT record in your DNS configuration: {0}", "2-add-txt-record": "2- Add TXT record", "go-to-your-domain-registrar-log-into-your-account-and-find-the-dns-records-section": "Go to your domain registrar, log into your account and find the DNS records section.", "1-setup": "1- Setup", "continue": "Continue", "example-3-top-level-domain-s4-s3-cloudsky-be-allows-you-to-create-dns-records-to": "Example 3: top level domain s4.s3.cloudsky.be allows you to create DNS records to", "example-2-top-level-domain-cloudsky-be-allows-you-to-create-dns-records-to": "Example 2: top level domain cloudsky.be allows you to create DNS records to", "example-1-top-level-domain-s3-cloudsky-be-allows-you-to-create-dns-records-to": "Example 1: top level domain s3.cloudsky.be allows you to create DNS records to", "it-also-allows-you-to-freely-define-dns-records-of-any-type": "It also allows you to freely define DNS records of any type.", "a-top-level-domain-is-a-domain-name-to-and-under-which-the-system-can-create-domain-name-records-dns-records-for-the-external-ip-addresses-of-your-cloud-resources-cloudspaces-objectspaces-virtual-machines": "A top level domain is a domain name to and under which the system can create domain name records (DNS records) for the external IP addresses of your cloud resources (cloudspaces / objectspaces / virtual machines).", "configure-your-top-level-domain": "Configure your top level domain", "validate-ns": "Validate NS", "validate-domain-ownership": "Validate domain ownership", "top-level-domain": "Top level domain", "delete-demo-user-configuration": "Delete demo user configuration", "users-can-can-log-into-the-portal-as-a-demo-user": "users can can log into the portal as a demo user", "demo-user-is-set-with-this-link": "Demo user is set. With this link", "users-can-can-log-into-the-portal-on-behalf-of-the-demo-user": "users can can log into the portal on behalf of the demo user", "with-this-link": "With this link", "username": "Username", "any-user-will-be-automatically-logged-in-to-the-portal-on-behalf-of-the-demo-user": "any user will be automatically logged in to the portal on behalf of the demo user.", "now-demo-portal-is-configured-and-ready-to-use-with-this-link": "Now demo portal is configured and ready to use. With this link", "roles-organization": "role's organization", "now-on-behalf-of-the-demo-customer-admin-add-demo-user-to-the": ", now on behalf of the demo customer admin, add demo user to the", "page-of-the-role-organization": "page of the role organization", "for-the-redirect-to-the": "for the redirect to the", "access-management": "Access management", "access-to-the-demo-customer": "access to the demo customer", "to-give-members-of-the-role": "to give members of the role", "grant-customer-access": "Grant customer access", "documentation-on-role-management": "documentation on role management", "permission-only-see": "permission only. See", "read": "Read", "role-with": "role with", "to-create-a-new": "to create a new", "add-role": "Add role", "click": "Click", "create-the-resources-required-for-the-demo-portal-e-g-assign-a-location-create-a-cloudspace-create-a-vm": "Create the resources required for the demo portal, e.g.: assign a location, create a cloudspace, create a VM", "customer-is-selected-as-a-current-context-in-the-top-bar-navigate-to": "customer is selected as a current context in the top bar, navigate to", "demo": "Demo", "ensure-that-your": "Ensure that your", "and-create-a-new-customer-for-demo-purposes": "and create a new customer for demo purposes", "cloud-admin-greater-than-customers": "Cloud admin > Customers", "log-in-back-to-the-portal-as-a-portal-admin-and-paste-credentials-obtained-in-the-previous-step-in-the-form-below": "Log in back to the portal as a portal admin and paste credentials obtained in the previous step in the form below", "of-the-access-token": "of the access token", "app-secret": "App Secret", "app-id": "App ID", "of-the-demo-user": "of the demo user,", "access-token": "access token", "and-create-an": "and create an", "settings-greater-than-access-tokens": "Settings>Access tokens", "navigate-to": "navigate to", "in": "In", "on-behalf-of-the-demo-user": "on behalf of the demo user", "log-in-to": "Log in to", "that-will-be-used-as-a-demo-user": "that will be used as a demo user", "create-a-new-user-in": "Create a new user in", "how-to-configure": "How to configure:", "demo-user-functionality-allows-unauthorized-users-to-navigate-in-the-portal-without-having-to-log-in": "Demo user functionality allows unauthorized users to navigate in the portal without having to log in.", "set-demo-user": "Set demo user", "license-incompliancy-forwarding-is-updated-successfully": "License incompliancy forwarding is updated successfully", "notifications-forwarding-is-updated-successfully": "Notifications forwarding is updated successfully", "self-registration-configuration-is-updated-successfully": "Self registration configuration is updated successfully!", "there-is-no-payment-configuration-to-enable-attaching-credit-card-please-configure-a-payment-method-from-admin-greater-than-settings-greater-than-payment-providers-greater-than-configure": "There is no payment configuration to enable attaching credit card. Please configure a payment method from Admin > Settings > Payment providers > Configure", "notify-customers-for-windows-vm-incompliancy": "Notify customers for Windows VM incompliancy.", "license-incompliancy-emails-forwarding": "License incompliancy emails forwarding.", "new-customers-default-show-prices": "New customers default show prices", "show-prices": "Show prices", "forward-notifications-to-customers": "Forward notifications to customers.", "notifications-forwarding": "Notifications forwarding.", "users-need-to-wait-until-their-new-customer-account-has-been-approved-by-the-portal-admins": "Users need to wait until their new customer account has been approved by the portal admins.", "self-registration-require-admin-approval": "Self-registration: require admin approval.", "add-a-required-step-to-self-registration-wizard-to-configure-payment-method": "Add a required step to self-registration wizard to configure payment method.", "self-registration-payment-information": "Self-registration: payment information.", "activate-self-registration-wizard-and-enable-users-to-create-new-customer-accounts": "Activate self-registration wizard and enable users to create new customer accounts.", "users-can-contact-support-directly-from-the-portal-this-allows-you-to-provide-easy-to-access-first-level-support-to-your-customers": "Users can contact support directly from the portal. This allows you to provide easy to access first level support to your customers.", "live-chat": "Live chat.", "contact-whitesky-cloud-support-to-enable-live-chat-feature-as-it-requires-some-manual-configuration-on-the-chat-whitesky-cloud-backend-to-allow-your-support-people-to-use-the-chat-whitesky-cloud-for-engaging-with-your-customers-via-chat": "Contact whitesky.cloud support to enable live chat feature as it requires some manual configuration on the chat.whitesky.cloud backend to allow your support people to use the chat.whitesky.cloud for engaging with your customers via chat.", "payment-terms": "Payment terms", "legal-terms": "Legal terms", "numbering-format": "Numbering format", "resource-quotas-updated-successfully": "Resource quotas updated successfully", "public-ip": "PUBLIC IP", "vdisk-space-gib": "VDISK SPACE (GiB)", "memory-gib": "MEMORY (GiB)", "confirm": "Confirm", "update-unlimited-customer-quotas": "Update unlimited customer quotas", "update-customers-with-standard-quotas": "Update customers with standard quotas", "update-customers-with-custom-quotas": "Update customers with custom quotas", "update-all-customer-quotas": "Update all customer quotas", "please-confirm-updating-resource-quotas": "Please confirm updating resource quotas", "update-resource-quota": "Update Resource Quota", "error-message": "Error message", "default-error-message-for-new-customers-when-resources-are-exceeded-markdown-is-available": "Default error message for new customers when resources are exceeded (Markdown is available):", "configure-error-message": "Configure error message", "disallowed-tags-attributes-and-styles-will-be-removed-from-the-input-only-allowed-tags-attributes-and-styles-will-be-retained-in-the-html": "Disallowed tags, attributes, and styles will be removed from the input. Only allowed tags, attributes, and styles will be retained in the HTML.", "you-can-use-text-markdown-or-html-see-the-markdown-guide": "You can use text, markdown or HTML, see the markdown guide", "all-links-will-be-clickable-and-will-open-in-a-new-tab": "All links will be clickable and will open in a new tab.", "here-you-can-add-a-message-to-guide-customers-when-resource-limits-are-reached": "Here you can add a message to guide customers when resource limits are reached.", "resources-set-to-0-means-no-limit": "Resources set to 0 means no limit.", "resource-quota": "Resource Quota", "emergency-notifications": "Emergency Notifications", "support": "Support", "resource-quotas": "Resource Quotas", "audits": "Audits", "demo-user": "Demo user", "payment-providers": "Payment Providers", "branding": "Branding", "general": "General", "spla-settings-were-updated-successfully": "Spla settings were updated successfully!", "unset-spla-settings": "Unset SPLA settings", "edit-spla-settings": "Edit SPLA settings", "submit": "Submit", "spla-contract-number": "SPLA contract number", "email": "Email", "name": "Name", "spla-services-provider-license-agreement-is-a-licensing-program-offered-by-microsoft-that-allows-cloud-providers-and-service-providers-to-offer-microsoft-software-products-and-services-to-their-customers-on-a-subscription-basis-it-provides-flexibility-and-cost-effectiveness-eliminating-the-need-for-upfront-license-purchases-with-spla-partners-can-deliver-a-wide-range-of-microsoft-software-solutions-to-their-customers-while-ensuring-compliance-and-staying-up-to-date-with-the-latest-software-versions-it-enables-service-providers-to-expand-their-offerings-enhance-customer-experiences-and-simplify-software-licensing-management": "SPLA (Services Provider License Agreement) is a licensing program offered by Microsoft that allows cloud providers and service providers to offer Microsoft software products and services to their customers on a subscription basis. It provides flexibility and cost-effectiveness, eliminating the need for upfront license purchases. With SPLA, partners can deliver a wide range of Microsoft software solutions to their customers while ensuring compliance and staying up-to-date with the latest software versions. It enables service providers to expand their offerings, enhance customer experiences, and simplify software licensing management.", "spla-contract-settings": "SPLA contract settings", "support-guide-updated-successfully": "Support guide updated successfully", "support-organization": "support organization", "support-text": "Support text", "update-customer-support-guide": "Update customer support guide:", "here-you-can-add-instructions-for-the-customers-on-how-to-contact-your-help-desk": "Here you can add instructions for the customers on how to contact your help-desk.", "adding-support-guide": "Adding support guide", "top-level-domain-updated-successfully": "Top level domain updated successfully", "deleted-successfully": "Deleted successfully", "error-in-domain-configuration": "Error in domain configuration.", "configuration-for-domain-is-set-correctly": "Configuration for domain is set correctly", "configure-dns": "configure DNS", "validate-top-level-domain": "Validate top level domain", "delete-top-level-domain": "Delete top level domain", "configure-with-steps": "Configure with steps", "error-in-domain-validations-show-configuration-steps": "Error in domain validations, show configuration steps", "otherwise-the-cloud-resource-using-this-domain-will-not-resolve": "otherwise the cloud resource using this domain will not resolve!", "error-in-configuration-please-make-sure-that-your-top-level-domain-points-to-the-following-ns-nameserver": "Error in configuration, please make sure that your top level domain points to the following NS (nameserver):", "please-configure-a-top-level-domain-to-allow-your-customers-to-use-it-for-their-deployed-cloud-resource": "Please configure a top level domain to allow your customers to use it for their deployed cloud resource.", "your-top-level-domain-is-not-configured-yet": "Your top level domain is not configured yet!", "self-registration": "Self registration.", "saved": "Saved!", "please-confirm-deleting-configured-spla-settings": "Please confirm deleting configured SPLA settings", "are-you-sure-you-want-to-delete-the-top-level-domain": "Are you sure you want to delete the top level domain?", "certificate-for-domain-this-domain-this-domain-this-customerresourcedomain-this-customerresourcedomain-is-successfully-added": "Certificate for Domain: {0} is successfully added!", "certificate-for-domain-data-domain-is-successfully-added": "Certificate for Domain: {0} is successfully added!", "certificates": "Certificates", "available-top-level-domains": "Available top level domains", "subdomain-optional": "Subdomain (optional)", "private-key": "Private key", "certificate": "Certificate", "create-using-letsencrypt": "Create using letsencrypt", "create-using-certificate-and-key": "Create using certificate and key", "certificate-info": "Certificate info", "certificate-type": "Certificate type", "update-certificate-for": "Update Certificate for", "add-record": "Add record", "dns-record-created": "DNS record created", "this-this-record_type-record-already-exists-for-this-domain-if-several-hosts-are-configured-with-the-same-domain-round-robin-scheduling-will-be-applied-to-distribute-incoming-requests-for-this-domain-among-the-hosts-are-you-sure-you-want-to-proceed": "This {0} record already exists for this domain. If several hosts are configured with the same domain, Round Robin scheduling will be applied to distribute incoming requests for this domain among the hosts. Are you sure you want to proceed?", "weight-0-or-higher": "Weight 0 or higher.", "weight": "Weight", "port-number-e-g-80": "Port number e.g. 80", "port": "Port", "service-e-g-_smtp-or-_ftp": "Service e.g.(_smtp or _ftp)", "service": "Service", "protocol-e-g-_udp-or-_tcp": "Protocol e.g.(_udp or _tcp)", "protocol": "Protocol", "priority-0-or-higher": "Priority: 0 or higher", "priority": "Priority", "e-g-example-com": "e.g. 'example.com'", "domain": "Domain", "e-g-issue-issuewild-or-iodef": "e.g. 'issue', 'issuewild' or 'iodef'", "tag": "Tag *", "flag": "Flag *", "ip-address": "IP address", "record-type": "Record type", "add-dns-record-to-customer": "Add DNS record to Customer", "dns-configuration": "DNS configuration", "follow-the-instructions-to-add-this-ns-records-in-your-dns-configuration": "Follow the instructions to add this NS records in your DNS configuration:", "follow-the-instructions-to-add-this-txt-record-in-your-dns-configuration-for-your-root-domain-domainverificationcode": "Follow the instructions to add this TXT record in your DNS configuration for your root domain: {0}", "add-domain": "Add Domain", "cloud-admin": "Cloud admin", "customers": "Customers", "audit": "Audit", "content": "Content", "response": "Response", "body": "Body", "arguments": "Arguments", "request": "Request", "user-agent": "User agent", "fallback": "Fallback", "x-forwarded-for": "X forwarded for", "origin": "Origin", "audit-response-response_time-tofixed-2-seconds": "{0} seconds", "response-time": "Response time", "requested-at": "Requested at", "resource-id": "resource id", "resource-type": "resource type", "customer-id": "customer id", "user-name": "User name", "path": "Path", "details": "Details", "ended-before": "Ended before", "started-after": "Started after", "response-time-s": "Response time (s)", "user": "User", "method": "Method", "include-get-requests": "Include GET requests", "deleting": "Deleting", "update": "Update", "certificate-store": "Certificate Store", "chain": "Chain", "in-getremainingdays-certificate-expires_at-days": "(in {0} days)", "expires-at": "Expires at", "created-at": "Created at", "created-by": "Created by", "please-confirm-deleting-certificate-item-domain": "Please confirm deleting certificate {0}:", "delete-certificate": "Delete certificate", "add-certificate": "Add Certificate", "added-at": "Added at", "added-by": "Added by", "source": "Source", "in-getremainingdays-props-item-expires_at-days": "(in {0} days)", "license-subsccription-is-updated-successfully": "License subsccription is updated successfully", "license-notification": "License notification", "software-label": "software label", "cloudspace-name": "cloudspace name", "virtual-machine-name": "virtual machine name", "sample-json": "<PERSON><PERSON>:", "test": "Test", "post-notification-also-to-url": "Post notification also to url", "when-a-new-microsoft-software-is-detected": "when a new Microsoft software is detected.", "send-a-notification-to-the-customer-contact-email": "Send a notification to the customer contact email", "email-notification": "Email notification.", "delete-dns-records": "Delete dns records", "dns-record-deleted-successfully": "Dns record deleted successfully", "delete-dns-record": "Delete DNS record", "item-domain-deleted-successfully": "{0} deleted successfully", "not-available": "Not available", "add-dns-record": "Add DNS record", "add-top-level-domain": "Add Top level domain", "validate-selected-top-level-domains": "Validate selected top level domains", "delete-selected-dns-records": "Delete selected DNS records", "delete-selected-top-level-domains": "Delete selected top level domains", "dns-records": "DNS records", "top-level-domains": "top level domains", "valid": "<PERSON><PERSON>", "virtual-machine": "Virtual machine", "cloudspace": "Cloudspace", "validate": "Validate", "edit-top-level-domain": "Edit top level domain", "please-configure-a-top-level-domain-to-allow-using-it-for-your-deployed-cloud-resource": "Please configure a top level domain to allow using it for your deployed cloud resource.", "resources-top-level-domain-editted": "Resources top level domain editted", "customers-compliance": "Customers Compliance", "issues-count": "Issues count", "create": "Create", "certificate-for-domain-data-domain-is-successfully-updated": "Certificate for Domain: {0} is successfully updated!", "software-licenses": "Software Licenses", "count": "Count", "certificate-is-successfully-removed": "Certificate is successfully Removed!", "are-you-sure-you-want-to-delete-the-selected-top-level-domains": "Are you sure you want to delete the selected top level domains?", "are-you-sure-you-want-to-delete-this-dns-record": "Are you sure you want to delete this DNS record?", "are-you-sure-you-want-to-delete-the-selected-dns-records": "Are you sure you want to delete the selected DNS records?", "in-this-page-you-can-view-and-limit-the-amount-of-resources-that-can-be-used-by-each-customer": "in this page you can view and limit the amount of resources that can be used by each customer", "invalid-color": "Invalid color input. Please enter a valid color in one of the following formats: a hex code (e.g. #ffffff), an RGB value (e.g. rgb(255, 255, 255)), or a lowercase color name (e.g. 'red', 'blue', etc.).", "added-on": "Added on", "default-value": "Default value", "dns-sec": "DNSSEC", "view-ds-record": "View DS record", "ds-record-information": "DS Record Information", "add-ds-record": "Please add the following DS record to your domain registrar:", "dns-sec-info": "To enable DNSSEC (Domain Name System Security Extensions) for your domain and ensure the security of your DNS records, you need to add the following DS record to your domain registrar. This record acts as a cryptographic signature that validates your domain's DNS information, protecting it from spoofing and other attacks.", "no-providers-available": "No providers available", "loading-providers": "Loading providers", "add-provider": "Add provider", "update-status": "Update provider status", "delete provider": "Delete provider", "valid-url": "\"Must be a valid url\"", "update-provider": "Update OIDC Provider", "oidc": {"provider": "OIDC Provider", "providers": "OIDC Providers", "create": "Create", "edit": "Edit", "add": "Add", "update": "Update", "name": "Name", "name-help": "A unique name for this OIDC provider", "issuer-url": "Issuer URL", "issuer-url-help": "The URL of the OIDC provider's issuer endpoint (will be used to discover endpoints via .well-known)", "client-id": "Client ID", "client-id-help": "The client ID provided by the OIDC provider", "client-secret": "Client Secret", "client-secret-help": "The client secret provided by the OIDC provider", "fixed-scopes": "Fixed scopes", "fixed-scopes-help": "Space-separated list of needed OAuth2 scopes that will be sent with every request", "fixed-scopes-placeholder": "openid profile email", "optional-scopes": "Optional scopes", "optional-scopes-help": "Space-separated list of additional OAuth2 scopes to request", "optional-scopes-placeholder": "optional scopes", "claim-key": "<PERSON><PERSON><PERSON>", "claim-key-help": "Optional claim name to validate for access control (e.g., 'organization')", "claim-key-placeholder": "organization", "claim-value": "Claim Value", "claim-value-help": "Required value for the claim key (e.g., 'cairo-cloud')", "claim-value-placeholder": "cairo-cloud", "redirect-url": "Redirect URL", "redirect-url-help": "Use this redirect URL in your OIDC provider configuration", "copy-to-clipboard": "Copy to clipboard", "field-required": "This field is required", "provider-success": "Provider {0} successfully", "created": "created", "updated": "updated", "redirect-url-copied": "Redirect URL copied to clipboard", "password-login-enabled": "Password Login Enabled", "password-login-disabled": "Password Login Disabled", "active": "Active", "inactive": "Inactive", "issuer": "Issuer", "status": "Status", "actions": "Actions", "add-provider": "Add Provider", "password-login-success": "Password login {0} successfully", "enabled": "enabled", "disabled": "disabled", "provider-status-success": "Provider {0} successfully", "activated": "activated", "deactivated": "deactivated", "delete-provider-confirm": "Are you sure you want to delete this provider?", "delete-provider-warning": "This action cannot be undone.", "provider-deleted-success": "Provider deleted successfully", "help": {"title": "OIDC Provider Configuration Help", "description": "OIDC (OpenID Connect) providers allow users to authenticate using external identity services like Google, Microsoft, or custom OAuth2 providers. Configure the fields below to enable single sign-on for your users.", "docs-link": "/docs/en/oidc-setup", "view-docs": "View Full Documentation", "google-example": {"title": "Google OIDC Configuration Example", "step1": "1. Create Google OAuth2 Application", "step1-desc": "Go to Google Cloud Console > APIs & Services > Credentials and create a new OAuth2 client ID for a web application.", "step2": "2. Configure Authorized Redirect URIs", "step2-desc": "Add your redirect URL (shown after creating the provider) to the authorized redirect URIs in your Google OAuth2 application.", "step3": "3. Configure Provider <PERSON>s", "client-id-example": "Your Google Client ID (e.g., 123456789-abc.apps.googleusercontent.com)", "client-secret-example": "Your Google Client Secret from the credentials page", "redirect-note": "Important:", "redirect-desc": "Copy the redirect URL after creating the provider and add it to your Google OAuth2 application's authorized redirect URIs."}}, "providers-help": {"title": "OIDC Providers Management", "description": "Manage your OpenID Connect (OIDC) identity providers. These providers allow users to authenticate using external services instead of traditional username/password login.", "security-title": "Enhanced Security", "security-desc": "OIDC providers offer improved security through centralized authentication, multi-factor authentication support, and reduced password management overhead.", "management-title": "User Management", "management-desc": "Easily control user access by activating/deactivating providers and optionally restricting access based on user claims like organization membership."}}}, "base": {"note-this-title-is-created-successfully": "Note {0} is created successfully!", "please-fill-in-all-fields-first": "Please fill in all fields first!", "content": "Content *", "add-new-note": "Add new note", "alert": "<PERSON><PERSON>", "here": "Here", "you-dont-have-access-to-customers-yet-create-a-customer-from": "You don't have access to customers yet, create a customer from", "welcome": "Welcome", "authorization": "Authorization", "sorry-we-couldnt-open-auth-popup-please-allow-it-or-try-other-browser": "Sorry! we couldn't open auth popup, please allow it or try other browser", "authorize": "Authorize", "authorization-required": "Authorization Required", "id-of-el-is-copied-to-the-clipboard": "Id of {0} is copied to the clipboard!", "breadcrumbs": "Breadcrumbs", "code": "Code", "reason": "Reason", "enter-the-reason-for-your-action": "Enter the reason for your action", "no-items-available": "No items available", "loading-items": "Loading items...", "table": "Table", "search": "Search", "next": "Next", "previous": "Previous", "selected-items-selectedcount": "- selected items: {0}", "page-footerpagination-page-of-math-max-footerpagination-pagecount-1-total-results-footerpagination-itemslength": "Page {0} of {1}, Total results {2}", "date-time": "Date time", "filter-results-select-filter-type": "Filter results, select filter type", "filter-by": "Filter by", "year": "Year", "help-title-is-not-set": "Help title is not set", "help": "Help", "help-message-is-not-set": "Help message is not set", "note-this-note-title-is-deleted-successfully": "Note {0} is deleted successfully!", "please-confirm-deleting-note-this-note-title": "Please confirm deleting note {0} :", "delete-note": "Delete note", "note-this-note-title-is-updated-successfully": "Note {0} is updated successfully!", "update-note": "Update note", "or-last-modified-by": "Last modified by:", "last-modified-on": "Last modified on:", "created-on": "Created on:", "click-to-navigate-link-title-to-link-title": "Click to navigate {0}", "objectspaces": "Objectspaces", "disks": "Disks", "vmmanagement": "VMManagement", "vms": "VMs", "virtual-machines": "Virtual machines", "cloudspaces": "Cloudspaces", "audit-response-response_time-tofixed-2-seconds": "{0} seconds", "failed-to-get-audits-information": "Failed to get audits information", "click-to-copy": "Click to copy", "and": "&", "new-note": "New note", "resource-limit-reached": "Resource limit reached", "slider": "Slide<PERSON>", "next-run-will-be-at": "Next run will be at", "message-level-message-message": ": {0} : {1}", "copied-to-the-clipboard": "copied to the clipboard!", "more": "more"}, "customer": {"in-this-page-you-can-view-and-limit-the-amount-of-resources-that-can-be-used-by-this-customer": "In this page, you can view and limit the amount of resources that can be used by this customer", "add-organization-first": "Please add organization first", "role-this-payload-name-is-successfuly-this-roleid-updated-added": "Role {0} is successfuly {1}!", "name-is-a-required-field": "Name is a required field!", "roles": "Roles", "roles-and-permissions": "Roles & permissions", "execute": "Execute", "permissions": "Permissions", "optional-description": "optional description", "name": "Name *", "basic-configurations": "Basic configurations", "role-rolename-is-successfully-granted-to-this-resourcetype-this-resourcename": "Role {0} is successfully granted to {1} {2}!", "virtual-machine": "Virtual Machine", "license-name": "License Name", "error-message-when-resources-are-exceeded-markdown-is-available": "Error message when resources are exceeded (Markdown is available):", "here-you-can-add-message-to-guide-customer-when-resources-limits-is-reached": "Here you can add message to guide customer when resources' limits is reached.", "notify-customer-for-windows-vm-incompliancy": "Notify customer for Windows VM incompliancy.", "emergency-contact-updated-successfully": "Emergency contact updated successfully", "preferences-updated-successfully": "Preferences updated successfully", "contact-by-phone": "Contact By Phone", "contact-by-email": "Contact By Email", "select-all": "Select All", "subscribe-to-emergency-notifications": "Subscribe to emergency notifications", "alert-phone": "Alert phone", "alert-email": "Alert email", "emergency-notification-contact-info": "Emergency notification contact info", "emergency-notifications-are-sent-in-case-your-vms-were-migrated-from-one-compute-node-to-another-and-rebooted-you-can-receive-notifications-by-email-or-with-a-phone-call-to-your-support-line": "Emergency notifications are sent in case your VMs were migrated from one compute node to another and rebooted. You can receive notifications by email or with a phone call to your support line.", "emergency-notifications": "Emergency notifications", "preferences": "Preferences", "location-related-notification-types": "Location related notification types", "in-all-available-locations": "in all available locations", "subscribe-to": "Subscribe to", "general-notification-types": "General notification types", "role-this-role-name-access-is-successfully-revoked-from-resource_type-resource_name": "Role {0} access is successfully revoked from {1} {2}!", "please-confirm-revoking-role-this-role-name-access-from-resource_type-resource_name": "Please confirm revoking role {0} access from {1}: {2}", "role-this-role-name-is-successfully-granted-to-customer-this-activecustomer": "Role {0} is successfully granted to customer {1}!", "please-confirm-granting-role-this-role-name-to-customer-this-activecustomer": "Please confirm granting role {0} to customer {1}", "grant-access": "Grant access", "manage-members": "manage members", "revoke-access": "Revoke access", "iam-organization": "IAM organization", "role-item-name-access-is-successfully-revoked-from-this-resourcetype-this-resourcename": "Role {0} access is successfully revoked from {1} {2}!", "please-confirm-revoking-role-item-name-access-from-this-resourcetype-this-resourcename": "Please confirm revoking role {0} access from {1}: {2}", "role": "Role", "role-this-role-name-access-is-revoked-from-item-resource_type-item-resource_type-customer-this-activecustomer-item-resource_name-successfully": "Role {0} access is revoked from {1} {2} successfully!", "please-confirm-revoking-role-this-role-name-access-from-item-resource_type-item-resource_type-customer-this-activecustomer-item-resource_name": "Please confirm revoking role {0} access from {1} {2}", "role-this-role-name-is-successfully-deleted": "Role {0} is successfully deleted!", "please-confirm-deleting-role-this-role-name": "Please confirm deleting role {0}:", "delete-role": "Delete Role", "disk": "Disk", "objectspace": "Objectspace", "this-role-doesnt-grant-access-to-any-resource": "This role doesn't grant access to any resource", "resources-item-resource_type": "{0} :", "grants": "<PERSON>s", "only-customer-admins-can-perform-this-action": "Only customer admins can perform this action!", "support-organization-deleted-successfully": "Support organization deleted successfully", "support-organization-added-successfully": "Support organization added successfully", "role-item-name-is-successfully-deleted": "Role {0} is successfully deleted!", "please-confirm-deleting-role-item-name": "Please confirm deleting role {0}:", "grant-admin-access-to-the-hostname-support-crew": "<PERSON> admin access to the {0} support crew", "support-guide": "Support guide", "no-documentation-added": "No documentation added", "here": "here", "failed-to-upload-this-file": "Failed to upload this file", "cd-rom-this-name-is-successfully-uploaded": "CD-ROM {0} is successfully uploaded.", "please-complete-all-fields-first": "Please complete all fields first!", "please-choose-cd-rom-source": "Please choose CD-ROM source", "please-fill-in-all-fields": "Please fill in all fields", "cd-rom-images": "CD-ROM images", "add-cd-rom-image": "Add CD-ROM image", "cd-rom-image": "CD-ROM Image", "cd-rom-image-url": "CD-ROM image URL *", "upload-cd-rom-from-a-local-file": "Upload CD-ROM from a local file", "upload-cd-rom-from-a-url": "Upload CD-ROM from a URL", "os-name": "OS name *", "os-type": "OS type *", "cd-rom-image-name": "CD-ROM image name *", "create-cd-rom": "Create CD-ROM", "cd-rom-source": "CD-ROM source", "configuration-details": "Configuration details", "virtual-machine-image-this-name-is-successfully-uploaded": "Virtual machine image {0} is successfully uploaded.", "please-choose-image-source": "Please choose image source", "invalid-memory-size-memory-should-be-multiple-of-128": "Invalid memory size. Memory should be multiple of 128", "vmimages": "VMImages", "virtual-machine-images": "Virtual machine images", "qcow2-qcow-raw-vdi-vmdk-vhdx": ".qcow2, .qcow, .raw, .vdi, .vmdk, vhdx", "add-virtual-machine-image": "Add Virtual machine image", "image": "Image", "virtual-machine-image-url": "Virtual machine image url *", "upload-image-from-a-local-file": "Upload image from a local file", "upload-image-from-a-url": "Upload image from a URL", "boot-disk-size-gib": "Boot disk size (GiB)", "memory-mib": "Memory (MiB)", "boot-type": "Boot type *", "virtual-machine-image-name": "Virtual machine image name *", "create-image": "Create Image", "image-source": "Image source", "and-boot-disk-size": "& Boot disk size", "memory": "Memory", "cdrom-image-this-cdrominfo-name-is-successfully-deleted": "CDROM image {0} is successfully deleted!", "please-confirm-deleting-cdrom-image-this-cdrominfo-name": "Please confirm deleting CDROM image: {0}", "delete-cdrom-image-image": "Delete CDROM image image", "cdrom-images": "CDROM images", "no-documentation-yet": "No documentation yet", "documentation-url": "Documentation URL:", "cdrom-image-item-name-is-successfully-deleted": "CDROM image {0} is successfully deleted!", "please-confirm-deleting-cdrom-image-item-name": "Please confirm deleting CDROM image: {0}", "cdrom-image-name": "CDROM image name", "vm-images": "VM Images", "grant-role-access": "grant role access", "add-cdrom-image": "Add  CDROM image", "currency": "<PERSON><PERSON><PERSON><PERSON>", "used-total-public-ip": "Used/Total (Public IP)", "used-total-disk-space-gb": "Used/Total  (Disk space GB)", "used-total-memory-gb": "Used/Total (Memory GB)", "used-total-vcpu": "Used/Total (Vcpu)", "virtual-machine-image-this-imageinfo-name-is-successfully-deleted": "Virtual machine image {0} is successfully deleted!", "please-confirm-deleting-virtual-machine-image-this-imageinfo-name": "Please confirm deleting virtual machine image: {0}", "delete-virtual-machine-image": "Delete virtual machine image", "boot-disk-size": "Boot disk size", "virtual-machine-image-item-name-is-successfully-deleted": "Virtual machine image {0} is successfully deleted!", "please-confirm-deleting-virtual-machine-image-item-name": "Please confirm deleting virtual machine image: {0}", "vm-image-name": "VM image name", "cards": "Cards", "address-not-geocodable-please-modify-it": "Address not geocodable, please modify it.", "stripe": "Stripe", "flutterwave": "Flutterwave", "paymob": "Paymob", "route": "Route", "is-your-billing-address-err-response-data-address": "Is your billing address, {0}?", "is-your-company-address-err-response-data-address": "Is your company address, {0}?", "billing-address-not-geocodable": "Billing address not geocodable", "company-address-not-geocodable": "Company address not geocodable", "invalid-address": "Invalid address", "please-fill-in-all-required-fields-first": "Please fill in all required fields first!", "approved": "Approved", "pin-location-on-map": "Pin location on map", "no": "No", "yes": "Yes", "home": "Home", "read-the-documentation": "Read the documentation", "get-started": "Get started", "lets-get-this-show-on-the-road": "let's get this show on the road!", "your-customer-account-is-successfully-created": "Your customer account is successfully created", "you-can-take-advantage-of-this-waiting-time-by-reading-the-documentation-until-one-of-our-admins-approves-your-customer-account-registration": "You can take advantage of this waiting time by reading the documentation until one of our admins approves your customer account registration.", "your-customer-account-is-successfully-created-and-waiting-for-approval": "Your customer account is successfully created and waiting for approval.", "done": "Done", "we-use-the-paymentconfig-payment_gateway-payment-provider-for-secured-and-automated-payments-for-security-reasons-we-do-not-store-any-of-your-credit-card-information-ourselves-by-attaching-your-credit-card-our-automated-billing-and-invoicing-system-is-able-to-settle-your-balance-automatically-at-the-end-of-the-month": "We use the {0} payment provider for secured and automated payments. For security reasons, we do not store any of your credit card information ourselves. By attaching your credit card, our automated billing and invoicing system is able to settle your balance automatically at the end of the month.", "something-went-wrong-during-attaching-your-credit-card-please-try-again": "something went wrong during attaching your credit card, Please try again", "failed": "Failed", "note-that-registration-process-can-only-be-finalized-after-adding-your-credit-card-details": "Note that registration process can only be finalized after adding your credit card details.", "use-pinned-location": "Use pinned location", "press-enter-to-search-for-address": "Press enter to search for address", "vat-number": "VAT number", "billing-address": "Billing address *", "billing-info": "Billing Info", "billing-contact-phone-number": "Billing contact phone number *", "billing-contact-email": "Billing contact email *", "billing-contact-name": "Billing contact name *", "billing-contact": "Billing Contact", "company-address": "Company address *", "company-name": "Company name *", "company-info": "Company Info", "contact-phone-number": "Contact phone number *", "contact-email": "Contact email *", "last-name": "Last name *", "first-name": "First name *", "contact-info": "Contact Info", "payment-information": "Payment information", "customer-account-information": "Customer account information", "register-customer-account": "Register customer account", "my-customer-accounts": "My customer accounts", "create-account": "Create Account", "lets-get-down-to-business": "let's get down to business!", "login-or-create-account": "Login or Create Account ⟶", "payment-gateway-is-configured": "Payment gateway is configured!", "payment-providers": "Payment providers", "sumbit": "Sumbit", "secret-api-key": "Secret api key *", "public-api-key": "Public api key *", "moto-integration-id": "Moto integration id *", "iframe-id": "Iframe id *", "hmac-secret": "HMAC secret *", "auth-capture-integration-id": "Auth capture integration id *", "api-key": "Api key *", "configuration": "Configuration", "payment-gateway": "Payment gateway", "customer-customer-name-registration-is-canceled-successfully": "Customer {0} registration is canceled successfully!", "please-confirm-cancelling-registration-of-the-customer-customer-name": "Please confirm cancelling registration of the customer {0}:", "cancel-registration": "Cancel registration", "awaiting-approval": "Awaiting approval", "active": "Active", "awaiting-credit-card-information": "Awaiting credit card information", "subscribe-for-our-news-and-updates": "Subscribe for our news and updates", "check-out-our-apis": "check out our APIs", "read-our-documentation": "read our documentation", "manage-your-profile": "manage your profile", "register-your-customer-account-here-to-profit-from-all-the-advantages-we-offer-you": "Register your customer account here to profit from all the advantages we offer you.", "to-register-a-new-customer-and-start-using-cloud-resources-contact": "To register a new customer and start using cloud resources, contact", "id-name-address-phone-or-status": "Id, name, address, phone or status", "you-dont-have-any-customer-accounts-yet": "You don't have any customer accounts yet!", "hello-customername": "Hello {0} !", "payment-provider": "Payment provider", "api-secret-key": "Api secret key", "no-configuration-is-set-please-configure-payment-gateway-first": "No configuration is set, please configure payment gateway first", "configure": "Configure", "remove-payment-configuration": "Remove payment configuration", "please-confirm-removing-this-paymentgateway-payment-configuration-note-that-customers-morecustomers-showncustomers-join-and-morecustomers-more-showncustomers-join-will-be-impacted": "Please confirm removing {0} payment configuration. Note that customers:  {1} will be impacted", "and": ", and", "payment-configuration-is-successfully-removed": "Payment configuration is successfully removed!", "this-card-will-be-used-to-pay-your-balance-if-charging-the-default-card-fails": "This card will be used to pay your balance if charging the default card fails.", "this-card-will-be-used-to-pay-your-balance": "This card will be used to pay your balance.", "default": "<PERSON><PERSON><PERSON>", "mark-as-default": "Mark as default", "delete": "Delete", "add-credit-card": "Add credit card", "credit-card-attached-successfully": "Credit card attached successfully!", "failed-to-attach-credit-card-please-try-again": "Failed to attach credit card. Please try again", "mark-card-as-default": "Mark card as default", "please-confirm-marking-card-card-masked_pan-or-or-xxxx-xxxx-xxxx-card-last4-as-default": "Please confirm marking card {0} as default:", "card-card-masked_pan-or-or-xxxx-xxxx-xxxx-card-last4-is-successfully-marked-as-default": "Card {0} is successfully marked as default!", "delete-card": "Delete card", "please-confirm-deleting-card-card-masked_pan-or-or-xxxx-xxxx-xxxx-card-last4": "Please confirm deleting card {0} :", "card-card-masked_pan-or-or-xxxx-xxxx-xxxx-card-last4-is-successfully-deleted": "Card {0} is successfully deleted!", "no-description": "Description is not set."}, "cloudspace": {"please-confirm-removing-external-network": "Please confirm removing external network {0}:", "can-not-delete-peer-of-type-connected-cloudspaces": "Can not delete peer of type connected-cloudspaces", "go-to-peer-details-page": "Go to peer details page", "reachable-ips": "Reachable IPs", "peer-name-should-be-longer": "Peer name should be longer than two characters", "persistent-keepAlive": "Persistent keep alive(optional)", "invalid-mtu": "Invalid MTU", "invalid-private-key": "Invalid private key", "invalid-port-number": "Iinvalid port number", "if-you-are-not-migrating-an-existing-wireguard-vpn-network-it-is-recommended-to-let-the-keys-to-be-generated-by-the-platform-this-avoids-the-private-wireguard-key-to-be-transmitted-over-the-network-and-thus-is-the-most-secure-option": ". If you are not migrating an existing WireGuard VPN network it is recommended to let  the keys to be generated by the platform. This avoids the private WireGuard key to be transmitted over the network and thus is the most secure option.", "public-and-private-keys-are-optional": "Public and private keys are optional", "public-and-private-keys": "Public and private keys", "maximum-transmission-size-of-each-packet-of-data-that-your-network-can-handle-most-of-the-time-the-default-mtu-of-1420-bytes-works-well-for-most-networks": "The maximum transmission unit (MTU) represents the largest size of a data packet that can be transmitted over your network. In most cases, a default MTU of 1420 bytes is optimal and performs efficiently across a wide range of network configurations", "listening-port-for-incoming-connections-if-not-configured-or-set-to-zero-wireguard-will-choose-random-non-busy-port": "listening port for incoming connections, if not configured or set to zero, WireGuard will choose random non busy port.", "port-number-0": "Port number:", "choosing-port-and-mtu-numbers": "Choosing port and MTU numbers", "suggest-ip-network": "Suggest IP Network", "select": "Select", "choosing-interface-address": "Choosing interface address", "create-interface": "Create interface", "add-key-pair": "Add key pair", "port-number": "Port number", "an-address-within-a-private-ip-range-such-as-10-0-0-0-24-or-172-16-0-0-24-when-choosing-an-address-make-sure-it-doesnt-conflict-with-any-existing-ips-in-your-cloudspace-networks-or-your-peer-device-0": "an address within a private IP range, such as 10.0.0.0/24 or **********/24. When choosing an address, make sure it doesn't conflict with any existing IPs in your cloudspace networks or your peer device.", "keep-alive-must-be-a-positive-value": "Keep alive must be a positive value", "invalid-allowed-ips": "Iinvalid Allowed IPs", "download-config": "Download config", "to-configure-wireguard-interface-on-your-device-withe-the-cloudspace-as-a-peer": "to configure WireGuard interface on your device withe the cloudspace as a peer.", "download-configuration-file-and-follow-instruction-of": "Download configuration file and follow instruction of", "download-config-file-and-follow-tutorial": "Download config file and follow tutorial", "persistent-keepalive-optional": "'persistent keep alive(optional)'", "end-point-optional": "'End point(optional)'", "clicking-on-suggest-ip-network-button-will-choose-a-network-that-doesnt-conflict-with-other-networks-in-your-cloudspace": "By clicking the 'Suggest IP Network' button, the system will automatically choose a network that does not overlap with others in your cloudspace.", "an-address-within-a-private-ip-range-such-as-10-0-0-0-24-or-172-16-0-0-24-when-choosing-an-address-make-sure-it-doesnt-conflict-with-any-existing-ips-in-your-cloudspace-networks-or-your-peer-device": "an available address from a private IP range, such as 10.0.0.0/24 or **********/24. Ensure the selected address does not conflict with any existing IPs in your cloudspace networks or connected peer devices.", "if-you-are-not-migrating-an-existing-wireguard-vpn-network-it-is-recommended-to-let-the-keys-to-be-generated-by-the-platform-this-avoids-the-private-wireguard-key-to-be-transmitted-over-the-network-and-thus-is-the-most-secure-option-0": ". If you are not migrating an existing WireGuard VPN network, it is highly recommended to allow the platform to generate the keys. This ensures that the private WireGuard key is never transmitted over the network, providing the highest level of security.", "listening-port-for-incoming-connections-if-not-configured-or-set-to-zero-wireguard-will-choose-random-non-busy-port-0": "Listening port for incoming connections, if not configured or set to zero, WireGuard will choose random non busy port.", "if-no-packets": "If no packets are received, the network is free from conflicts and can be safely used.", "to-verify-selected-network": "To verify that the selected network is safe to use for the WireGuard interface, open your terminal and run the following command:", "key-pairs-are-generated-directly-on-the-client-side-ensuring-that-your-private-keys-are-never-transmitted-over-the-network-this-guarantees-that-your-keys-remain-safe-and-secure-as-they-are-created-and-stored-locally-on-your-device": "Key pairs are generated directly on the client side, ensuring that your private keys are never transmitted over the network. This guarantees maximum security, as the keys are created and securely stored locally on your device.", "helps-maintain-the-connection-by-sending-periodic-packets-to-the-interface-a-common-value-is-25-seconds-its-useful-for-ensuring-if-the-peer-is-behind-a-nat-or-firewall": "This setting helps maintain the connection by sending periodic keep-alive packets to the interface. A commonly used interval is 25 seconds. It is particularly useful for ensuring connectivity when the peer is located behind a NAT or firewall.", "your-peer-device-address-and-port-e-g-192-168-1-1-51820-this-optional-as-long-as-you-added-the-cloudspace-endpoint-in-your-peers-configuration-just-copy-config-file-in-next-step": "Your peer device address and port (e.g., ***********:51820). This is optional as long as you have added the cloudspace endpoint in your peer’s configuration (you can simply copy the config file in the next step). Typically, this should be a static IP address or a domain name, which is commonly used for site-to-site peering setups.", "keepalive-and-endpoint": "Keep alive and endpoint", "the-nat-checkbox-if-the-peer-is-behind-a-nat-network-address-translation-device-this-lets-the-interface-know-that-it-should-handle-the-peers-connection-accordingly": "the NAT checkbox if the peer is behind a NAT (Network Address Translation) device. This lets the interface know that it should handle the peer’s connection accordingly.", "check": "Check", "list-of-ip-addresses-or-ranges-that-the-peer-can-send-and-receive-traffic-from-by-default-an-available-ip-address-within-the-interface-network-is-automatically-provided-below": "List of IP addresses or ranges that the peer can send and receive traffic from. By default, an available IP address within the interface network is automatically provided below.", "add-key-manually": "Add key manually", "generate-key-pair-automatically": "Generate key pair automatically", "you-can-choose-to-provide-your-public-key-manually": ", you can choose to provide your public key manually.", "alternatively": "Alternatively", "peer-key-pair": "Peer Key Pair", "download-configuration-file": "Download configuration file", "and-keep-alive": "& keep alive", "tutorial-on-how-to-setup-wireguard-interface-on-your-device": "Tutorial on how to setup WireGuard interface on your device", "configuration-to-add-the-interface-as-a-peer-in-your-device-interface": "Configuration to add the interface as a peer in your device interface", "interface-configuration-as-a-peer": "Interface configuration as a peer", "click-to-copy-public-key-props-item-public_key": "Click to copy public key:", "preview-mode": "This feature is in preview mode as we continue to polish and perfect it", "no-ip-networks-available": "No IP networks available", "loading-peer-ip-networks": "Loading peer IP networks...", "example-25-seconds": "Example: 25 seconds", "example-10-101-108-135-51524": "Example: **************:51524", "ip_example": "Example: ***********/24", "port_example": "Example: 51820", "mtu_example": "Example: 1420", "if-not-provided-will-be-generated-automatically": "If not provided will be generated automatically", "success-delete-ips": "Selected IPs is successfully deleted!", "confirm-delete-ips": "Please confirm deleting selected IPs", "peer_endpoint_update_msg": "Peer endpoint updated successfully!", "peer_keepalive_update_msg": "Peer keep alive updated successfully!", "peer_name_update_msg": "Peer name updated successfully!", "copied": "Copied!", "deleting-peers-msg": "Peers are deleted successfully!", "loading_wg_interfaces": "Loading WireGuard interfaces...", "loading_wg_peers": "Loading WireGuard peers...", "wg_not_data_text": "No connected WireGuard interfaces available", "wg_peers_not_data_text": "No connected peers available", "wg_name_update_msg": "WireGuard name updated successfully!", "wg_address_update_msg": "WireGuard address updated successfully!", "wg_mtu_update_msg": "<PERSON><PERSON><PERSON> mtu updated successfully!", "wg_port_update_msg": "WireGuard port updated successfully!", "please-confirm-deleting-selected-wireguard-interfaces": "Please confirm deleting selected WireGuard interfaces", "delete-wireguard-interfaces": "Delete WireGuard interfaces", "delete-selected-wireguard-interfaces": "Delete selected WireGuard interfaces", "add-wireguard-interface": "Add WireGuard Interface", "wireguard-vpns": "WireGuard VPNs", "allowed-ip-must-have-at-least-one-network": "Allowed IP must have at least one network", "peer-this-peer-name-is-successfully-updated": "Peer {0} is successfully updated!", "delete-ips": "Delete IPs", "ip-item-network-is-successfully-deleted": "IP {0} is successfully deleted!", "please-confirm-deleting-ip-network-item-network": "Please confirm deleting IP network {0}", "delete-allowed-ip": "Delete Allowed IP", "delete-selected-ips": "Delete Selected IPs", "wireguards": "Wireguard VPNs", "add-ip": "Add IP", "cloudspace.endpoint": "Endpoint", "keep-alive": "Keep alive", "invalid-ip-address": "Invalid IP address", "wireguard-this-wireguardconfig-name-is-successfully-created": "Wireguard {0} is successfully created!", "public-key-optional": "Public Key of the WireGuard Interface", "private-key-optional": "Private Key of the WireGuard Interface", "private-key": "Private Key", "maximum-transmission-unit-bytes": "Maximum Transmission Unit (bytes)", "the-listening-port-for-incoming-connections": "The listening port for incoming connections", "wireguard-interface-address": "WireGuard interface address", "interface-address": "Interface Address *", "name-of-the-wireguard-interface": "Name of the WireGuard interface", "wireguard-interface-configuration": "Wireguard Interface Configuration", "invalid-interface-address": "Invalid Interface address", "peer-this-peer-name-is-successfully-created": "Peer {0} is successfully created!", "invalid-public-key": "Invalid public key", "ingress.port-number-should-not-above-65535": "Port number should not above 65535", "enable-nat-traversal": "Enable NAT Traversal", "the-allowed-ip-that-will-route-through-the-vpn": "the allowed IP that will route through the VPN", "allowed-ip": "Allowed IP *", "add-allowed-ip": "Add Allowed IP", "the-persistent-keepalive-interval-in-seconds": "The persistent keep alive interval in seconds", "ip-address-and-port-of-the-peer": "IP address and port of the peer", "end-point": "End Point(optional)", "public-key-of-the-wireguard-peer": "public key of the WireGuard peer", "public-key*": "Public key*", "public-key": "Public key", "peer-name": "Peer name", "customer.name": "Name *", "add-peers": "Add P<PERSON>", "wireguard-item-name-is-successfully-deleted": "WireGuard {0} is successfully deleted!", "please-confirm-deleting-wireguard-interface-item-name": "Please confirm deleting WireGuard interface: {0}", "delete-wireguard-interface": "Delete WireGuard interface", "please-confirm-deleting-selected-peers": "Please confirm deleting selected peers", "delete-peers": "Delete Peers", "peer-item-name-is-successfully-deleted": "Peer {0} is successfully deleted!", "please-confirm-deleting-peer-item-name": "Please confirm deleting peer: {0}", "delete-peer": "Delete Peer", "public-key-copied-to-clipboard": "Public key copied to clipboard!", "mdi-mdi-account-plus": "mdi mdi-account-plus", "add-peer": "<PERSON><PERSON>", "delete-selected-peers": "Delete Selected Peers", "peers": "Peers", "allowed-ips": "Allowed IPs", "endpoint": "Endpoint", "wgmanagement": "WGManagement", "virtual-machine-this-vmname-successfully-added-to-group-this-groupid": "Virtual machine {0} successfully added to group {1}!", "select-virtual-machine-first": "Select virtual machine first!", "there-are-no-virtual-machines-available": "There are no virtual machines available", "select-virtual-machine-you-want-to-add-to-group-this-groupid": "Select virtual machine you want to add to group {0}", "storage": "Storage", "dns": "Dns", "dns-record-created-successfully": "DNS record created successfully", "this-this-record_type-record-already-exists-for-this-domain-if-several-hosts-are-configured-with-the-same-domain-round-robin-scheduling-will-be-applied-to-distribute-incoming-requests-for-this-domain-among-the-hosts-are-you-sure-you-want-to-proceed": "This {0} record already exists for this domain. If several hosts are configured with the same domain, Round Robin scheduling will be applied to distribute incoming requests for this domain among the hosts. Are you sure you want to proceed?", "select-external-ip-address": "Select external IP address", "a-top-level-domain-is-a-domain-name-to-and-under-which-the-system-can-create-domain-name-records-dns-records-for-the-external-ip-addresses-of-your-cloud-resources-cloudspaces-objectspaces-virtual-machines": "A top level domain is a domain name to and under which the system can create domain name records (DNS records) for the external IP addresses of your cloud resources (cloudspaces / objectspaces / virtual machines).", "add-dns-record-to-cloudspace": "Add DNS record to cloudspace", "external-networks": "External networks", "external-network-added-successfuly": "External network added successfuly", "external-network-ip": "External network ip", "mode": "Mode", "public": "Public", "subnet-mask": "Subnet Mask", "gateway": "Gateway", "network": "Network", "add-external-network": "Add External Network", "ip-address-optional": "IP address (optional)", "external-network-metric-lowest-value-has-highest-priority": "external network metric(Lowest value has highest priority)", "metric-optional": "Metric (optional)", "extra-configuration-optional": "Extra configuration (Optional):", "cloudspace-as-network": "Cloudspace as network", "external-network": "External network", "select-the-network-to-attach-to-cloudspace-cs-name": "Select the network to attach to cloudspace {0}:", "network-routes": "Network Routes", "network-route-added-successfuly": "Network route added successfuly", "choose-an-network-route": "Choose an network route", "routing-table-to-add-route-to": "Routing table to add route to", "network-route-metric-lowest-value-has-highest-priority": "Network route metric(Lowest value has highest priority)", "gateway-to-route-destination-over": "Gateway to route destination over", "destination-network-to-route": "Destination network to route", "destination": "Destination", "add-network-route": "Add network route", "more-information-on-creating-routing-tables": "more information on creating routing tables", "routing-tables": "Routing tables", "configuring-routing-rules-requires-knowledge-about-ip-routing-see": "Configuring routing rules requires knowledge about ip routing. See", "group-this-groupid-successfully-deleted": "Group {0} successfully deleted!", "please-confirm-deleting-group-this-groupid": "Please confirm deleting group {0} :", "delete-anti-affinity-group": "Delete anti-affinity-group", "add-virtual-machine": "Add virtual machine", "label": "Label", "spread": "Spread", "group-item-group_id-successfully-deleted": "Group {0} successfully deleted!", "please-confirm-deleting-group-item-group_id": "Please confirm deleting group {0} :", "virtual-machine-item-vm_name-has-been-removed-from-group-this-groupid": "Virtual machine {0} has been removed from group {1}!", "are-you-sure-you-want-to-remove-virtual-machine-item-vm_name-from-group-this-groupid": "Are you sure you want to remove virtual machine {0} from group {1}?", "remove-virtual-machine": "Remove virtual machine", "remove-from-group": "remove from group", "private-network": "Private network", "parent-ip": "Parent IP", "item-domain_name-deleted-successfully": "{0} deleted successfully", "please-confirm-deleting-selected-dns-records": "Please confirm deleting selected DNS records:", "delete-dns-records": "Delete DNS records", "dns-is-not-supported-for-nested-cloudspaces": "DNS is not supported for nested cloudspaces", "dns-is-not-supported-for-private-cloudspaces": "DNS is not supported for private cloudspaces", "remove-external-network": "Remove External network", "external-network-updated-successfuly": "External network updated successfuly", "external-network-removed-successfuly": "External network removed successfuly", "could-not-fetch-cloudspace-name": "Could not fetch cloudspace name", "no-external-network": "No external network", "update-external-network": "Update External Network", "metric": "Metric", "select-external-network": "Select External Network", "update-external-network-metric": "Update external network metric", "failed-to-get-cloudspace-name": "failed to get cloudspace name", "are-you-sure-you-want-to-disable-cloudspace-name-this-will-stop-all-virtual-machines-inside-and-stop-its-virtual-firewall": "Are you sure you want to disable cloudspace {0}? This will stop all virtual machines inside and stop its virtual firewall.", "please-confirm-deleting-cloudspace-name": "Please confirm deleting cloudspace {0} :", "port-forward-is-deleted-successfully": "Port forward is deleted successfully!", "please-confirm-deleting-selected-port-forwards": "Please confirm deleting selected port forwards:", "delete-port-forwards": "Delete port forwards", "please-confirm-stop-selected-virtual-machines": "Please confirm stop selected virtual machines:", "stop-virtual-machines": "Stop Virtual machines", "please-confirm-start-selected-virtual-machines": "Please confirm start selected virtual machines:", "start-virtual-machines": "Start Virtual machines", "please-confirm-deleting-selected-virtual-machines": "Please confirm deleting selected virtual machines:", "delete-virtual-machines": "Delete Virtual machines", "please-confirm-removing-selected-network-routes": "Please confirm removing selected network routes:", "remove-netowrk-routes": "Remove netowrk routes", "please-confirm-removing-selected-external-networks": "Please confirm removing selected external networks:", "remove-external-netowrks": "Remove external netowrks", "unset-localdomain": "Unset localdomain", "reset": "Reset", "create-load-balancer": "Create Load Balancer", "create-reverse-proxy": "Create Reverse Proxy", "create-server-pool": "Create Server Pool", "create-anti-affinity-group": "Create anti-affinity-group", "connect-to-remote-cloudspace": "Connect To Remote Cloudspace", "remove-network-routes": "Remove Network routes", "remove-external-networks": "Remove external networks", "create-port-forward": "Create port forward", "create-vm": "Create VM", "delete-cloudspace": "Delete Cloudspace", "enable-cloudspace": "Enable Cloudspace", "disable-cloudspace": "Disable Cloudspace", "delete-selected-portforwards": "Delete selected <PERSON><PERSON><PERSON><PERSON><PERSON>", "stop-selected-vms": "Stop selected VMs", "resume-selected-vms": "Resume selected VMs", "delete-selected-vms": "Delete selected VMs", "nested-cloudspaces": "Nested Cloudspaces", "anti-affinity-groups": "Anti-affinity groups", "port-forwards": "Port forwards", "connected-vms": "Connected VMs", "connected-cloudspaces": "Connected Cloudspaces", "ingress": "Ingress", "subnet": "Subnet", "default-external-ip": "Default External IP", "parent-cloudspace": "Parent cloudspace", "default-gateway": "Default gateway", "local-domain": "Local domain", "please-note-that-it-is-not-possible-to-create-a-virtual-machine-within-a-storage-only-cloud-location": "Please note that it is not possible to create a virtual machine within a storage-only cloud location.", "please-contact-support-of-hostname-to-resume-your-cloudspace": "Please contact support of {0} to resume your cloudspace", "network-route-removed-successfuly": "Network route removed successfuly", "please-confirm-removing-network-route-with-destination-item-destination": "Please confirm removing network route with destination {0}", "remove-network-route": "remove network route", "please-confirm-deleting-selected-cloudspaces": "Please confirm deleting selected cloudspaces:", "delete-cloudspaces": "Delete cloudspaces", "cloudspace-item-name-is-enabled-successfully": "Cloudspace {0} is enabled successfully!", "are-you-sure-you-want-to-enable-cloudspace-item-name": "Are you sure you want to enable cloudspace {0}?", "cloudspace-item-name-is-disabled-successfully": "Cloudspace {0} is disabled successfully!", "are-you-sure-you-want-to-disable-cloudspace-item-name-this-will-stop-all-virtual-machines-inside-and-stop-its-virtual-firewall": "Are you sure you want to disable cloudspace {0}? This will stop all virtual machines inside and stop its virtual firewall.", "cloudspace-item-name-is-deleted-successfully": "Cloudspace {0} is deleted successfully!", "please-confirm-deleting-cloudspace-item-name": "Please confirm deleting cloudspace {0} :", "create-cloudspace": "Create Cloudspace", "delete-selected-cloudspaces": "Delete selected Cloudspaces", "external-ip": "External IP", "enable": "Enable", "disable": "Disable", "cloudspace-item-target-is-disconnected-successfuly": "Cloudspace {0} is disconnected successfuly!", "please-confirm-disconnecting-cloudspace-item-target": "Please confirm disconnecting cloudspace {0}", "disconnect-cloudspace": "Disconnect cloudspace", "remote-subnet": "Remote subnet", "source-address": "Source address", "target-address": "Target address", "target-cloudspace": "Target cloudspace", "connected-cloudspaces-are-not-supported-for-nested-cloudspaces": "Connected cloudspaces are not supported for nested cloudspaces", "connected-cloudspaces-are-not-supported-for-private-cloudspaces": "Connected cloudspaces are not supported for private cloudspaces", "disconnect": "Disconnect", "ip-address-es": "IP address/es", "select-an-ip-address": "Select an ip address", "connect-cloudspace": "Connect cloudspace", "local-ip-address-public-ip-from-activecloudspace": "Local ip address (public ip from {0})", "local-ip-address": "Local IP address", "remote-ip-address": "Remote IP address", "remote-ip-address-public-ip-from-selectedname": "Remote ip address (public ip from {0})", "select-ip-address-endpoints": "Select IP address endpoints", "select-remote-cloudspace": "Select remote cloudspace", "group-created-successfully": "Group created successfully!", "infinite-spread": "infinite spread", "write-a-unique-name-for-group": "Write a unique name for group", "select-parent-cloudspace-first": "Select parent cloudspace first!", "select-virtual-firewall-first": "Select virtual firewall first!", "select-external-network-first": "Select external network first!", "choose-how-should-your-cloudspace-be-connected-first": "Choose how should your cloudspace be connected first!", "select-location-first": "Select location first!", "cloudspace-created-successfully": "Cloudspace created successfully!", "you-must-complete-missing-fields-first": "You must complete missing fields first!", "virtual-machine": "virtual machine", "select-top-level-domain": "Select top level domain", "connect-to-an-external-network": "Connect to an external network", "windows": "Windows", "select-type": "Select type", "id": "Id", "image-type": "Image type", "disk-size-gib": "Disk size (GiB)", "2-determine-disk-size-vcpus-and-memory": "2- Determine Disk size, vCPUs and memory:", "1-select-the-image": "1- Select the image:", "custom-firewall-configuration": "Custom firewall configuration:", "enter-a-name-for-your-cloudspace": "Enter a name for your cloudspace", "basic-configurations": "Basic configurations:", "not-connected": "Not connected", "choose-parent-cloudspace": "Choose parent cloudspace", "select-parent-cloudspace": "Select parent cloudspace:", "to-another-cloudspace-in-this-location": "To another cloudspace in this location", "using-a-custom-virtual-firewall-that-you-choose-and-configure-yourself": "Using a custom virtual firewall that you choose and configure yourself", "using-the-built-in-virtual-firewall-supports-port-forwards-reverse-proxy-load-balacing-and-connecting-to-remote-cloudspaces-using-the-builtin-virtual-firewall": "Using the built in virtual firewall  (supports port forwards, reverse proxy, load balacing & connecting to remote cloudspaces using the builtin virtual firewall)", "select-virtual-firewall": "Select virtual firewall:", "to-an-external-network-internet-your-company-network": "To an external network (internet, your company network, ...)", "how-should-your-cloudspace-be-connected": "How should your cloudspace be connected?", "location-on-map": "Location on map:", "address": "Address", "select-location": "Select location", "datacenter-information": "Datacenter information:", "select-the-location-where-you-want-to-deploy-your-cloudspace": "Select the location where you want to deploy your cloudspace:", "add-configuration-details": "Add configuration details", "port-forward-deleted-successfully": "Port forward deleted successfully!", "please-confirm-deleting-port-forward-with-id-this-portforwardid": "Please confirm deleting port forward with id {0} :", "delete-port-forward": "Delete port forward", "port-forward-with-id-this-updatemode-successfully-updated": "Port forward with id {0} successfully updated!", "port-forward-created-successfully": "Port forward created successfully", "nested-cloudspace": "Nested cloudspace", "to": "To", "end-port": "End port", "configure-forwarding-for-a-range-of-ports": "Configure forwarding for a range of ports", "start-port": "Start port", "external-virtual-firewall-port": "External virtual firewall port *", "select-ip-address": "Select IP address", "config": "Config", "proxy-logs": "Proxy logs", "load-balancers": "Load Balancers", "reverse-proxies": "Reverse Proxies", "server-pools": "Server Pools", "please-confirm-deleting-port-forward-with-id-item-portforward_id": "Please confirm deleting port forward with id {0} :", "resource-port": "Resource port", "resource-name": "Resource name", "external-virtual-firewall-ip": "External virtual firewall IP", "port-forwards-are-not-supported-for-private-cloudspaces": "Port forwards are not supported for private cloudspaces", "subdomain-optional": "Subdomain (optional)", "subdomain": "Subdomain", "nested-cloudspace-port": "Nested cloudspace port *", "virtual-machine-port": "Virtual machine port *", "cpu-usage": "CPU usage", "cpu-usage-help": "Note: 100% on this graph reflects full usage of a single CPU. For VMs with multiple vCPUs, values may exceed 100% to indicate combined consumption.", "current-number-of-cpu": "Current number of vCPUs is: ", "upgrade-traefik": "Upgrade proxy version", "confirm-upgrading-traefik": "Upgrading the proxy version will upgrade the Traefik version to the latest version (3.3). Are you sure you want to proceed?", "traefik-upgraded-successfully": "Proxy upgraded successfully!"}, "dialogs": {"rancher-management-cluster-is-deleted-successfully": "Rancher management cluster is deleted successfully!", "management-cluster-name": "Management cluster name *", "to-confirm-deleting": "to confirm deleting:", "please-type": "Please type", "view-resources": "View resources", "deleting-clusters-will-delete-all-resources-connected-to-it": "Deleting clusters will delete all resources connected to it", "therere-no-kubernetes-clusters-deployed-in": "There're no kubernetes clusters deployed in", "kubernetes-clusters-will-be-deleted-by-deleting-management-cluster": "Kubernetes clusters will be deleted by deleting management cluster:", "deleting-management-cluster-will-delete-any-kubernetes-clusters-deployed-in-it": "Deleting management cluster will delete any kubernetes clusters deployed in it.", "delete-management-cluster": "Delete management cluster", "vm-this-vmname-is-successfully-deleted": "VM {0} is successfully deleted!", "detach-vgpu": "Detach vGPU", "detach-disks": "Detach disks", "please-confirm-deleting-virtual-machine-vmname": "Please confirm deleting virtual machine {0}:", "delete-virtual-machine": "Delete virtual machine", "demo-user-credentials-were-set-successfully": "Demo user credentials were set successfully!", "anyone-can-access-this-portal-as-a-demo-user-with-no-fees-make-sure-you-set-read-only-permissions-for-the-demo-user": "anyone can access this portal as a demo user with no fees. Make sure you set read only permissions for the demo user.", "watch-provisioning": "WATCH PROVISIONING", "your-new-kubernetes-cluster-cluster-name-started-provisioning-this-takes-on-average-10-minutes-depending-on-network-speed": "Your new kubernetes cluster {0} started provisioning. This takes on average 10 minutes depending on network speed.", "cluster-dashboard": "Cluster dashboard", "your-new-management-cluster-is-created-successfully-you-can-start-managing-it-from-our-dashboard": "Your new management cluster is created successfully. You can start managing it from our dashboard!", "spla-settings-were-set-successfully": "Spla settings were set successfully!", "by-submitting-this-form-you-acknowledge-and-agree-to-take-responsibility-for-the-monthly-reporting-of-software-usage-to-microsoft-accurate-and-timely-reporting-is-essential-to-ensure-compliance-with-microsofts-licensing-terms-failure-to-report-usage-or-inaccuracies-in-reporting-may-lead-to-penalties-license-audits-or-other-consequences-as-outlined-in-the-spla-agreement-please-ensure-that-you-fully-understand-and-are-prepared-to-fulfill-your-reporting-obligations-before-proceeding": "By submitting this form you acknowledge and agree to take responsibility for the monthly reporting of software usage to Microsoft. Accurate and timely reporting is essential to ensure compliance with Microsoft's licensing terms. Failure to report usage or inaccuracies in reporting may lead to penalties, license audits, or other consequences as outlined in the SPLA agreement. Please ensure that you fully understand and are prepared to fulfill your reporting obligations before proceeding.", "set-spla-settings": "Set SPLA settings", "logo-is-updated-successfully": "Logo is updated successfully", "image-type-must-be-png": "Image type must be PNG", "logo-size-should-be-less-than-2-mb": "Logo size should be less than 2 MB!", "image-file": "Image file", "preview": "Preview", "upload": "Upload"}, "gpus": {"vgpu-is-attached-to-virtual-machine-successfully": "vGPU is attached to virtual machine successfully!", "vgpu-this-vgpuname-is-reserved-and-attached-to-virtual-machine-successfully": "vGPU {0} is reserved and attached to virtual machine successfully!", "please-select-gpu-profile-first": "Please select GPU profile first!", "please-select-vgpu-type-first": "Please select vGPU type first!", "virtual-machines": "Virtual Machines", "gpu": "Gpu", "available-instances": "Available instances", "model": "Model", "vendor": "<PERSON><PERSON><PERSON>", "vgpu-name": "vGPU name *", "enter-vgpu-name": "Enter vGPU name", "select-vgpu-profile": "Select vGPU profile", "select-gpu-profile": "Select GPU profile", "attach-vgpu": "Attach reserved vGPU Profile", "add-vgpu": "Reserve and Attach vGPU Profile", "basic-configuration": "Basic configuration", "vgpu-type": "vGPU type", "vgpu-is-reserved-successfully": "vGPU is reserved successfully!", "reserve": "Reserve", "available-gpu-profiles": "Available GPU profiles", "select-a-gpu-profile": "Select a GPU profile:", "detach-from-virtual-machine-item-virtual_machine-name": "Detach from virtual machine {0}", "delete-vgpu": "Delete vGPU", "vgpus-are-detached-successfully": "VGPUs are detached successfully!", "please-confirm-detaching-selected-vgpus-from-attached-virtual-machines": "Please confirm detaching selected vGPUs from attached virtual machines:", "detach-vgpus": "Detach vGPUs", "your-customer-this-activecustomer-does-not-have-access-to-any-locations-contact-your-administrator-to-request-the-access": "Your customer {0} does not have access to any locations. Contact your administrator to request the access.", "reserve-vgpu": "Reserve vGPU", "delete-selected": "Delete Selected", "detach": "<PERSON><PERSON>", "loading-gpus": "Loading GPUs", "no-gpus-available": "No GPUs available", "there-is-no-available-instances-in-this-gpu-profile": "There are no available instances in this gpu profile"}, "objectspace": {"buffer-size-updated-successfully": "Buffer size updated successfully!", "confirm-updating-buffer-disk-size-which-will-update-storage-cost-from-oldprice-tofixed-2-to-newprice-tofixed-2-data-currency": "Confirm updating buffer disk size which will update storage cost from {0} to {1} {2}", "update-buffer-size": "Update buffer size", "buffer-size-must-be-greater-than-zero": "Buffer size must be greater than zero", "buffer-size-0": "<PERSON><PERSON><PERSON>", "the-buffer-is-used-for-accepting-writes-on-nvme-storage-before-the-internal-spreading-via-erasure-coding-and-results-in-more-performance-it-is-only-to-be-used-when-needed": "The buffer is used for accepting writes on NVME storage before the internal spreading via erasure coding and results in more performance. It is only to be used when needed.", "buffer-size": "Buffer size", "please-confirm-deleting-bucket-access-credential-item-name": "Please confirm deleting bucket access credential: {0}", "deleting-bucket-access-credential": "deleting bucket access credential", "bucket-access-credentials-deleted-successfully": "Bucket access credentials deleted successfully", "please-confirm-deleting-selected-credentials": "Please confirm deleting selected credentials", "delete-access-credentials": "Delete access credentials", "credentials-updated": "Access credentials updated successfully!", "bucket-access-credentials": "Bucket Access Credentials", "delete-selected-access-credentials": "Delete Selected Access credentials", "add-access-key": "Add Access key", "objectspace.access-key": "Access key", "edit-access-credential": "Edit access credential", "click-to-copy-secret-key-props-item-secret_key": "Click to copy secret key: {0}", "click-to-copy-access-key-props-item-access_key": "Click to copy access key: {0}", "no-access-credentials-available": "No access credentials available", "loading-bucket-access-credentials-data": "Loading bucket access credentials data...", "limit": "Limit", "credentials-added": "Access credential added successfully!", "access-this-addaccesscredentials-name-type-name-cannot-be-empty": "Access {0} cannot be empty", "add-access-credentinals": "Add access credentinal", "read": "read", "write": "write", "read-write": "read + write", "access-type-0": "Access type", "access-type": "Access type *", "name-of-access-credential": "Name of access credential", "add-bucket-access-credential": "Add bucket access credential", "bucket-item-bucket_name-is-deleted-successfully": "Bucket {0} is deleted successfully!", "please-confirm-deleting-bucket-item-bucket_name": "Please confirm deleting bucket {0} :", "delete-bucket": "Delete bucket", "limit-iops": "Limit (IOPS)", "select-cloudspace-first": "Select cloudspace first!", "connect": "Connect", "choose-cloudspace-to-connect-to": "Choose cloudspace to connect to", "cloudspace-item-name-is-disconnected-from-objectspace-this-objectspace-objectspace_name-successfully": "Cloudspace {0} is disconnected from Objectspace {1} successfully!", "please-confirm-disconnecting-cloudspace-item-name-from-objectspace-this-objectspace-objectspace_name": "Please confirm disconnecting cloudspace {0} from objectspace {1}", "cloudspace-name": "Cloudspace name", "bucket-is-created-successfully": "Bucket is created successfully!", "bucket-limit-is-set-successfully": "Bucket limit is set successfully!", "buckets": "Buckets", "enter-bucket-name": "Enter bucket name", "select-external-network": "Select external network", "please-enter-objectspace-name-first": "Please enter objectspace name first!", "select-cloudspace": "Select cloudspace", "choose-a-cloudspace": "Choose a cloudspace", "create-new-cloudspace": "Create new cloudspace", "letsencrypt-email": "letsencrypt email", "enable-letsencrypt": "Enable letsencrypt", "a-top-level-domain-is-a-domain-name-to-and-under-which-the-system-can-create-domain-name-records-dns-records-for-the-external-ip-addresses-of-your-cloud-resources-cloudspaces-objectspaces-virtual-machines": "A top level domain is a domain name to and under which the system can create domain name records (DNS records) for the external IP addresses of your cloud resources (cloudspaces / objectspaces / virtual machines).", "create-objectspace": "Create objectspace", "make-publicly-available": "Make publicly available", "expresscheckout-select-cloudspace-create-new-cloudspace": "`+!expressCheckout?`Select cloudspace`:`Create new cloudspace", "cloudspace-optional": "Cloudspace (Optional)", "enter-objectspace-name": "Enter objectspace name", "select-the-location-where-you-want-to-create-your-objectspace": "Select the location where you want to create your objectspace:", "dns-configurations": "DNS configurations", "please-confirm-deleting-selected-objectspaces": "Please confirm deleting selected objectspaces:", "delete-objectspaces": "Delete objectspaces", "objectspace-item-objectspace_name-is-successfully-deleted": "Objectspace: {0} is successfully deleted!", "please-confirm-deleting-objectspace-item-objectspace_name": "Please confirm deleting objectspace {0} :", "delete-objectspace": "Delete objectspace", "delete-selected": "Delete selected", "failed-to-update-all-selected-buckets": "Failed to update all selected buckets", "buckets-limit-is-updated-successfully": "Buckets limit is updated successfully!", "failed-to-delete-all-selected-buckets": "Failed to delete all selected buckets", "selected-buckets-are-deleted-successfully": "Selected buckets are deleted successfully!", "please-confirm-deleting-selected-buckets": "Please confirm deleting selected buckets", "delete-buckets": "Delete Buckets", "objectspace-this-objectspace-objectspace_name-is-deleted-successfully": "Objectspace {0} is deleted successfully!", "please-confirm-deleting-objectspace-this-objectspace-objectspace_name": "Please confirm deleting objectspace {0} :", "are-you-sure-you-want-to-reset-access-key-and-secret-of-the-objectspace": "Are you sure you want to reset access key and secret of the objectspace ?", "reset-access-key-and-secret": "Reset access key and secret", "cloudspace-connections": "Cloudspace Connections", "add-bucket": "Add Bucket", "reset-credentials": "Reset Credentials", "update-selected-buckets-limit": "Update selected buckets limit", "delete-selected-buckets": "Delete selected <PERSON><PERSON>", "secret": "Secret", "access-key": "Access key", "set-new-limit-for-all-buckets": "Set new limit for all buckets", "update-buckets-limit": "Update buckets limit", "public-domain": "Public domain", "objectspace-item-objectspace_name-is-disconnected-from-cloudspace-this-cloudspacedetails-name-successfully": "Objectspace: {0} is disconnected from cloudspace: {1} successfully!", "please-confirm-disconnecting-objectspace-item-objectspace_name-from-cloudspace-this-cloudspacedetails-name": "Please confirm disconnecting objectspace: {0} from cloudspace: {1}", "disconnect-objectspace": "Disconnect objectspace", "use-new-cloudspace-network": "Use a new external  network ip address"}, "recyclebin": {"after-deleting-from-the-recycle-bin-resources-cannot-be-restored-please-confirm-permanently-deleting-resources": "After deleting from the recycle bin, resources cannot be restored! Please confirm permanently deleting resources.", "delete-bucket": "Delete Bucket", "resources-have-been-restored-successfully": "Resources have been restored successfully", "deletion-time": "Deletion time", "objectspace-status": "Objectspace status", "restore": "Rest<PERSON>", "resources-have-been-deleted": "Resources have been deleted", "delete-cdrom-images": "Delete CDROM images", "resources-were-restored-successfully": "Resources were restored successfully", "delete-cloud-space": "Delete Cloud Space", "delete-disk": "Delete disk", "delete-image": "Delete image", "boot-image-size": "Boot image size", "delete-virtual-machine": "Delete Virtual Machine", "cloudspace-status": "Cloudspace status", "vm-images": "VM images", "recycle-bin": "Recycle Bin", "showing-partial-results-as-the-communication-with-cloud-locations": "Showing partial results as the communication with cloud locations", "failed-during-building-the-cloudspaces-list": "failed during building the cloudspaces list.", "showing-partial-results-as-the-communication-with-cloud-location": "Showing partial results as the communication with cloud location"}, "utilities": {"select-cloudspace": "Select cloudspace", "create-new-cloudspace": "Create new cloudspace", "after-deleting-from-the-recycle-bin-resources-cannot-be-restored-please-confirm-permanently-deleting-resources": "After deleting from the recycle bin, resources cannot be restored! Please confirm permanently deleting resources.", "id-of-customer-this-customer-name-is-copied-to-clipboard": "Id of customer {0} is copied to clipboard", "unauthorized": "Unauthorized", "click-to-copy-id": "Click to copy ID:", "select-customer": "Select Customer", "active-customer": "Active customer", "invalid-value": "Invalid value!", "handler-apierrorhandler-nurl-url-nreason-err-ndatapassed-json-stringify-data": "Handler: APIErrorHandler\\nURL: {0}\\nReason: {1}\\nDataPassed: {2}", "handler-promiseuncaughtrejection-nreason-event-reason": "Handler: PromiseUncaughtRejection\\nReason: {0}", "handler-window-nmessage-msg-nfile-url-nline-line-ncolumn-col-nstacktrace-error-stack": "Handler: Window\\nMessage: {0}\\nFile: {1}\\nLine: {2}\\nColumn: {3}\\nStackTrace: {4}", "handler-vue-ninfo-info-nerror-error-stack": "Handler: Vue\\nInfo: {0}\\nError' {1}", "note": "Note", "dont-show-me-this-again": "Don't show me this again", "failed-during-building-the-resource-list": "failed during building the {0} list.", "price-per-month-in-currency": "Price per month in {0}", "resource": "Resource", "si": "Si", "monthly-price": "Monthly Price", "g8-name": "G8 Name", "license-wu": "License (WU):", "transaction-tu": "Transaction (TU)", "storage-su": "Storage (SU)", "vcpus-vcu": "VCPUs (VCU)", "memory-mu": "Memory (MU)", "price-details": "Price details:", "configuration-cost": "Configuration cost:", "consumption-details": "Consumption details:", "resources-quota": "Resources Quota", "resources-item-resource_type": "{0} :", "resource-not-found": "(Resource not found)", "select-the-role-you-want-to-grant-access-to-resourcename": "Select the role you want to grant access to: {0}", "step": "Step"}, "ingress": {"load-balancer-ip-s": "Load balancer IP(s)", "server-pool-id": "Server pool ID", "no-hosts-available-in-this-server-pool": "No Hosts available in this server pool", "load-balancer-s-ips": "Load balancer(s) IPs", "server-pool-name-updated-successfully": "Server Pool name updated successfully", "server-pool-description-updated-successfully": "Server Pool description updated successfully", "ref-is-required": "{0} is required", "load-balancer-this-loadbalancer-name-is-successfully-created": "Load Balancer {0} is successfully created!", "all-ip-addresses": "All IP addresses", "port-number-should-not-above-65535": "Port number should not above 65535", "target-port": "Target Port", "serverpool": "Serverpool *", "load-balancer-back-end": "<PERSON><PERSON> Balancer Back-End", "pass-through-no-tls-termination": "Pass Through (No TLS Termination)", "domain": "Domain *", "port": "Port *", "load-balancer-front-end": "Load Balancer Front-End", "description-of-the-load-balancer": "Description of the load balancer", "type": "Type *", "name-of-the-load-balancer": "Name of the load balancer", "load-balancer-general-information": "Load Balancer General Information", "reverse-proxy-this-reverseproxy-name-is-successfully-created": "Reverse proxy {0} is successfully created!", "proxy-back-end": "Proxy Back-End", "letsencrypt-email": "Letsencrypt email *", "use-letsencrypt": "Use Letsencrypt", "https-port": "Https Port *", "http-port": "Http Port *", "scheme": "Scheme", "domain-name-of-the-proxy": "Domain name of the proxy", "proxy-front-end": "Proxy Front-End", "description-of-the-reverseproxy": "Description of the Reverseproxy", "name-of-the-reverseproxy": "Name of the Reverseproxy", "proxy-general-information": "Proxy General Information", "server-pool-this-serverpool-name-is-successfully-created": "Server pool {0} is successfully created!", "description-of-the-serverpool": "Description of the Serverpool", "name-of-the-serverpool": "Name of the <PERSON>pool", "ingress-config-updated": "Ingress Config updated", "no-available-logs": "No available logs", "can-not-get-logs": "Can not get logs", "load-balancer-this-loadbalancer-name-is-successfully-updated": "Load balancer {0} is successfully updated!", "load-balancer-this-loadbalancer-name-is-successfully-deleted": "Load Balancer: {0} is successfully deleted!", "please-confirm-deleting-loadbalancer-this-loadbalancer-name": "Please confirm deleting loadbalancer: {0}", "delete-load-balancer": "Delete Load Balancer", "host-id": "Host ID", "serverpool-hosts": "Serverpool hosts", "load-balancer-item-name-is-successfully-deleted": "Load balancer: {0} is successfully deleted!", "please-confirm-deleting-load-balancer-item-name": "Please confirm deleting load balancer: {0}", "serverpool-name": "Serverpool name", "reverse-proxy-item-name-is-successfully-deleted": "Reverse proxy: {0} is successfully deleted!", "please-confirm-deleting-reverse-proxy-item-name": "Please confirm deleting reverse proxy: {0}", "delete-reverse-proxy": "Delete Reverse Proxy", "reverse-proxy-this-reverseproxy-name-is-successfully-updated": "Reverse proxy {0} is successfully updated!", "reverse-proxy-this-reverseproxy-name-is-successfully-deleted": "Reverse proxy: {0} is successfully deleted!", "please-confirm-deleting-reverseproxy-this-reverseproxy-name": "Please confirm deleting reverseproxy: {0}", "renew-certificate": "Renew certificate", "host-item-host_id-is-deleted-successfully": "Host {0} is deleted successfully!", "please-confirm-deleting-host-item-host_id": "Please confirm deleting host {0}:", "delete-host": "Delete host", "server-pool-this-serverpool-name-is-successfully-deleted": "Server pool: {0} is successfully deleted!", "please-confirm-deleting-serverpool-this-serverpool-name": "Please confirm deleting serverpool: {0}", "add-host": "Add Host", "delete-server-pool": "Delete Server Pool", "host-ip-is-successfully-added-to-this-server-pool": "Host: {0} is successfully added to this server pool!", "select-objectspace": "Select Objectspace:", "select-virtual-machine": "Select Virtual machine:", "ip-address": "Ip address", "ip-address-or-hostname": "ip address or hostname", "select-an-objectspace": "Select an objectspace", "select-a-vm": "Select a vm", "enter-ip-address-or-hostname": "Enter ip address or hostname", "enter-an-ip-address-or-hostname-or-select-a-vm-or-objectspace": "Enter an ip address or hostname, or select a vm or objectspace:", "host-this-hostaddress-is-successfully-removed": "Host: {0} is successfully removed!", "please-confirm-removing-host-this-hostaddress-from-server-pool-this-serverpool-name": "Please confirm removing host: {0} from server pool: {1}", "remove-host-from-server-pool": "Remove host from server pool", "host-this-hostid-not-found-in-this-server-pool": "Host {0} not found in this server pool", "remove-host": "Remove Host", "server-pool-item-name-is-successfully-deleted": "Server pool: {0} is successfully deleted!", "please-confirm-deleting-serverpool-item-name": "Please confirm deleting serverpool: {0}", "locked": "Locked", "unlock-load-balancer": "Unlock load balancer", "loadbalancer-unlocked-successfuly": "Loadbalancer {0} unlocked successfully!", "reverse-proxy-unlocked-successfuly": "Reverse proxy {0} unlocked successfully!", "unlock-reverse-proxy": "Unlock reverse proxy", "unlock-server-pool": "Unlock server pool", "server-pool-lock-updated": "Server pool is locked successfully", "server-pool-unlocked-successfuly": "Server pool {0} unlocked successfully!", "backend-scheme": "backend scheme", "to-confirm-unlocking": "to confirm unlocking.", "confirm-unlock": "Confirm unlock"}, "vm": {"show-expired": "Show expired", "workflow": "Workflow", "selected-policies-are-deleted-successfully": "selected backup policies are deleted successfully!", "please-confirm-unassigning-selected-policies": "please confirm unassigning selected backup policies", "unassign-policies": "Unassign backup policies", "calculating-time-left": "Calculating time left...", "unassign": "unassign", "policy-was-unassigned-successfully": "Policy {0} was unassigned successfully!", "please-confirm-unassigning-policy-item-policy-from-the-vm": "Please confirm unassigning policy {0} from the VM", "unassign-policy": "Unassign policy", "policy-id": "Policy ID", "policy-name": "Policy name", "backup-operation-id": "Backup operation ID:", "failure-details": "Failure Details", "end-time": "End Time", "start-time": "Start Time", "confirm-deleting-the-selected-backup-item-id": "Confirm deleting the selected backup: {0}", "delete-backup": "Delete Backup", "invalid-hostname": "Invalid hostname", "backup-restore-of-vm": "Backup restore of VM:", "some-vms-have-different-private-ip-subnet-than-the-original-one-are-you-sure-you-want-to-continue": "Some VMs have different private IP subnet than the original one, are you sure you want to continue?", "restore-virtual-machines-s": "Restore Virtual machines(s)", "restore-backups-completed-successfully": "Restore backups completed successfully!", "base.cloudspaces": "Cloudspaces", "backup-restore-progress": "Backup restore progress", "select-backup-and-configure-details": "Select Backup and configure details", "restore-from-backup": "<PERSON><PERSON> from backup", "backup-id": "Backup ID", "vm-name": "VM name", "new-vm-name": "New VM name", "cloudspace.id": "Id", "backup-status": "Backup Status", "progress": "Progress", "disk-type": "Disk Type", "disk-name": "Disk Name", "backup_policies-length-2-others": "(+{0} others)", "assign-backup-policy": "Assign backup policy", "restoring-backups-in-progress": "Restoring backup(s) in progress...", "recyclebin.restore": "Rest<PERSON>", "no-backup-was-found": "No Backup was found", "latest-backup": "Latest backup", "snapshot-time-0": "Snapshot time", "creation-timestamp": "Creation timestamp", "snapshot-time": "Snapshot time *", "syncing-backups-from-target": "Syncing backups from target...", "create-from-vm-backup": "Create from VM Backup", "loading-progress": "Loading progress...", "please-select-target-first": "Please select target first!", "select-backup-vm": "Select backup VM", "please-select-vm-first": "Please select VM(s) first!", "select-vm": "Select VM(s)", "assigned-policies": "Assigned backup policies", "select-backup-policy-to-create-backup": "Select backup policy to create backup", "filter-backup": "Filter backups", "select-backup-target": "Select backup Target", "are-you-sure-you-want-to-create-backup-assigned-to-default-policy": "Are you sure you want to create backup using backup policy {0}?", "create-backup": "Create Backup", "please-confirm-creating-instant-backup-on-this-vm": "Please confirm creating instant backup on this virtual machine using the defult policy set by admins:", "selected-backup-are-deleted-successfully": "selected backups are deleted successfully", "please-confirm-deleting-selected-backups": "please confirm deleting selected backups", "delete-backups": "Delete backups", "host-name": "Host Name", "this-a-record-already-exists-for-this-domain-if-several-hosts-are-configured-with-the-same-domain-round-robin-scheduling-will-be-applied-to-distribute-incoming-requests-for-this-domain-among-the-hosts-are-you-sure-you-want-to-proceed": "This A record already exists for this domain. If several hosts are configured with the same domain, Round Robin scheduling will be applied to distribute incoming requests for this domain among the hosts. Are you sure you want to proceed?", "batch-size": "Batch size", "start-virtual-machine-s-immediately-after-creation": "Start virtual machine(s) immediately after creation", "no-snapshots-available-for-boot-disk-bootdiskname": "No snapshots available for boot disk {0}", "loading-snapshots": "Loading snapshots...", "no-disks-available": "No disks available", "loading-disks": "Loading disks...", "no-images-available": "No images available", "loading-images": "Loading images...", "invalid-batch-size": "Invalid batch size", "number-of-VMs-in-batch": "number of VMs in batch", "create-batch": "Create Batch", "number-of-vms-couldnt-exceed-ip-limit": "Number of VMs could not exceed number of available IP addresses in the network", "for-dedicated-physical-storage-please-add-such-disks-after-creating-the-virtual-machine-to-access-specific-hardware-resources": "For dedicated physical storage, please add such disks after creating the virtual machine to access specific hardware resources.", "data-disks-added-during-virtual-machine-creation-are-configured-as-virtual-defined-storage-which-is-redundant-across-multiple-servers-and-storage-devices-for-enhanced-reliability": "Data disks added during virtual machine creation are configured as virtual defined storage, which is redundant across multiple servers and storage devices for enhanced reliability.", "data-disks": "Data disks", "dns-resources": "Dns Resources", "add-domain-to-cloudspace": "Add domain to cloudspace", "please-select-external-cloudspace-first": "Please select external cloudspace first!", "please-select-external-network-first": "Please select external network first!", "network-interface-this-selectedcs-name-or-or-this-selectednic-name-was-successfully-attached-to-virtual-machine-this-vmname": "Network Interface {0} was successfully attached to virtual machine {1}", "this-this-record_type-record-already-exists-for-this-domain-if-several-hosts-are-configured-with-the-same-domain-round-robin-scheduling-will-be-applied-to-distribute-incoming-requests-for-this-domain-among-the-hosts-are-you-sure-you-want-to-proceed": "This {0} record already exists for this domain. If several hosts are configured with the same domain, Round Robin scheduling will be applied to distribute incoming requests for this domain among the hosts. Are you sure you want to proceed?", "network-interfaces": "Network interfaces", "attach": "Attach", "add-dns-record-optional": "Add DNS record (optional):", "select-the-model-of-nic": "Select the model of NIC:", "select-ip-address": "Select IP address:", "select-the-network-to-attach-to-virtual-machine-vmname": "Select the network to attach to virtual machine {0}:", "cd-rom-image-this-cdromname-is-attached-to-virtual-machine-this-vm-name-successfully": "CD-ROM image {0} is attached to virtual machine {1} successfully!", "vmcdromimages": "VMCDROMImages", "select-the-cdrom-image-you-want-to-attach": "Select the cdrom image you want to attach:", "select-target-cloudspace-first": "Select target cloudspace first!", "select-snapshot-first": "Select snapshot first!", "virtual-machine-cloned-successfully-with-name-this-name": "Virtual machine cloned successfully with name: {0}!", "please-enter-a-name-for-cloned-virtual-machine-first": "Please enter a name for cloned virtual machine first!", "snapshot-creation-time": "Snapshot creation time", "snapshot-name": "Snapshot name", "clone-virtual-machine": "Clone virtual machine", "name-of-the-cloned-virtual-machine": "Name of the cloned virtual machine", "select-target-cloudspace": "Select target cloudspace:", "select-snapshot": "Select snapshot:", "enter-clone-details": "Enter Clone details", "virtual-machine-cpu-topology-is-updated-successfully": "Virtual machine cpu topology is updated successfully!", "number-of-vcpus-after-updating-topology-payload-cores-payload-sockets-payload-threads": "Number of vCPUs after updating topology : {0}", "sockets": "Sockets", "threads": "Threads", "cores": "Cores", "please-note-that-1-cpu-with-2-cores-and-2-threads-will-be-billed-as-4-vcpus": "Please note that 1 CPU with 2 cores and 2 threads will be billed as 4 vCPUs", "template-this-templatename-created-successfully": "Template {0} created successfully!", "please-enter-template-name-first": "Please enter Template name first!", "write-a-unique-name-for-the-template": "Write a unique name for the template", "template-name": "Template name *", "virtual-machines-created-successfully": "Virtual machines created successfully!", "virtual-machine-created-successfully": "Virtual machine created successfully!", "please-enter-correct-ssh-key": "Please enter correct ssh key", "please-enter-virtual-machine-name-first": "Please enter virtual machine name first!", "please-select-snapshot-first": "Please select snapshot first!", "please-fill-vgpu-information-first": "Please fill vGPU information first!", "please-select-image-first": "Please select image first!", "please-select-bacup-source-first": "Please select backup source first!", "please-select-boot-disk-first": "Please select Boot disk first!", "please-specify-s3-url-protocol-http-or-https": "Please specify S3 URL protocol (http or https)", "please-fill-all-required-fields-in-s3-information": "Please fill all required fields in S3 information!", "please-select-virtual-machine-source-first": "Please select virtual machine source first!", "size": "Size", "select-disk": "Select disk", "create-from-snapshot": "Create from snapshot", "storage-disabled": "Storage (disabled)", "s3-information": "S3 Information", "no-image-selected": "No image selected", "no-operating-system-installed": "No Operating system installed", "creation-time": "Creation time", "image-id": "Image id", "import-virtual-machine-as-close-as-possible-to-original-hardware": "Import virtual machine as close as possible to original hardware", "ssh-key-optional": "SSH key (optional)", "private-ip-address": "Private IP address", "virtual-machine-description": "Virtual Machine Description", "description-optional": "Description (optional)", "boot-type-is-defined-by-the-image": "Boot type is defined by the image", "operating-system-name": "Operating system name *", "operating-system-type": "Operating system type *", "virtual-machine-name": "Virtual machine name", "virtual-machine-host-name": "virtual machine host name", "add-configuration-details": "Add configuration details:", "data-disk": "Data disk", "data-disk-size-gib": "Data disk size (GiB)", "add-data-disks": "Add data disks:", "add-more-data-disks": "Add more data disks:", "available-vgpu-profiles": "Available vGPU profiles", "reserve-and-attach-gpu-profile-on-this-virtual-machine": "Reserve and attach gpu profile on this virtual machine", "gpu-profiles-are-not-available-on-location-if-you-want-to-use-virtual-machines-with-virtual-gpu-contact-store-state-userinfo-vco_support_email": "No GPU profile is available on {0}. If you want to use virtual machines with virtual GPU contact {1}", "attach-vgpu": "Attach vGPU:", "compute-size": "Compute size", "create-from-veeam-backup": "Create from veeam backup", "create-from-acronis-backup": "Create from acronis backup", "select-backup-source": "Select backup source:", "show-automatic-snapshots": "Show automatic snapshots", "clone-all-disks-created-along-with-the-selected-one": "Clone all disks created along with the selected one.", "select-boot-disk": "Select Boot Disk:", "name-of-the-stored-object-in-the-bucket": "Name of the stored object in the bucket", "object-name": "Object Name *", "name-of-the-bucket": "Name of the bucket", "bucket": "Bucket *", "region-of-the-s3-server": "region of the s3 server", "region": "Region *", "secret-to-access-the-server": "secret to access the server", "secret": "Secret *", "access-key-to-the-server": "access key to the server", "key": "Key *", "s3-server-link": "S3 server link", "link": "Link *", "select-cdrom-image": "Select CDROM image:", "select-image": "Select image", "create-from-boot-disk": "Create from boot disk", "create-from-cdrom-backup": "Create from cdrom backup", "create-from-a-snapshot": "Create from a snapshot", "import-from-s3": "Import from S3", "create-a-virtual-machine-without-operating-system-installed": "Create a virtual machine without operating system installed", "install-via-a-cdrom": "Install via a cdrom", "create-from-an-image": "Create from an image", "select-virtual-machine-source": "Select virtual machine source:", "note-operation-system-type-should-match-the-vm-that-was-backed-up": "Note: operation system type should match the VM that was backed up.", "cli-documentation": "CLI documentation", "note-recommended-way-to-import-vm-from-s3-storage-is-via-cli": "Note: recommended way to import VM from s3 storage is via CLI.", "the-specified-location-location-is-designated-as-a-storage-only-cloud-location-it-does-not-support-the-creation-of-virtual-machines": "The specified location, {0}, is designated as a storage-only cloud location. It does not support the creation of virtual machines.", "storage-only-location": "Storage only location", "virtual-machine-this-vminfo-name-is-successfully-exported-to-s3": "Virtual machine {0} is successfully exported to s3", "export-to-s3": "Export to s3", "bucket-name": "Bucket name", "secret-key": "Secret key", "note-recommended-way-to-export-vm-to-s3-storage-is-via-cli": "Note: recommended way to export VM to s3 storage is via CLI.", "virtual-machine-this-vminfo-name-is-successfully-moved-from-cloudspace-this-cloudspaceinfo-name-to-cloudspace-this-targetcsname": "Virtual machine {0} is successfully moved from cloudspace {1} to cloudspace {2}", "external-ip": "External ip", "move": "Move", "select-the-cloudspace-you-want-to-move-virtual-machine-vminfo-name-to": "Select the cloudspace you want to move virtual machine {0} to:", "cd-rom-this-cdrom-name-is-detached-from-virtual-machine-this-vm-name-successfully": "CD-ROM {0} is detached from virtual machine {1} successfully!", "are-you-sure-you-want-to-detach-cd-rom-image-this-cdrom-name-from-virtual-machine-this-vm-name": "Are you sure you want to detach CD-ROM image {0} from virtual machine {1} ?", "detach-cd-rom-image": "Detach CD-ROM image", "detach-from-this-vm-name": "Detach from {0}", "vmcdromimage": "VMCDROMImage", "disk-size": "Disk size", "cd-rom-item-name-is-detached-from-virtual-machine-this-vminfo-name-successfully": "CD-ROM {0} is detached from virtual machine {1} successfully!", "are-you-sure-you-want-to-detach-cd-rom-image-item-name-from-virtual-machine-this-vminfo-name": "Are you sure you want to detach CD-ROM image {0} from virtual machine {1} ?", "size-gib": "Size (GiB)", "console-container-view-canvas": "#console-container #view canvas", "enter": "Enter", "not-found": "not found", "failed-to-connect-to-the-console": "Failed to Connect to the Console", "vmconsole": "VMConsole", "send-text": "Send Text", "send-ctrl-alt-delete-to-the-vm": "Send ctrl-alt-delete to the vm?", "text-to-be-sent": "text to be sent", "send-to-vm-console": "Send to VM Console", "reconnect": "Reconnect", "disk-item-disk_name-is-successfully-detached-from-virtual-machine-item-vm_name": "Disk {0} is successfully detached from virtual machine {1}!", "please-confirm-detaching-disk-item-disk_name-from-virtual-machine-this-vminfo-name": "Please confirm detaching disk {0} from virtual machine {1} :", "detach-disk": "Detach Disk", "disk-item-disk_name-is-successfully-deleted": "Disk {0} is successfully deleted!", "please-confirm-deleting-disk-item-disk_name": "Please confirm deleting disk {0} :", "disk-item-disk_name-is-successfully-set-as-primary": "Disk {0} is successfully set as primary", "please-confirm-setting-disk-item-disk_name-as-boot-disk": "Please confirm setting disk {0} as boot disk :", "pci-slot": "PCI slot", "primary-boot": "Primary boot", "set-as-boot-disk": "Set as boot disk", "cloudspace-network": "Cloudspace Network", "mac-address": "MAC address", "device-name": "Device name", "network-name": "Network name", "virtual-machine-item-vmname-successfully-started": "Virtual machine {0} successfully started!", "are-you-sure-you-want-to-start-virtual-machine-item-vmname": "Are you sure you want to start virtual machine {0} ?", "start-virtual-machine": "Start Virtual machine", "virtual-machine-item-vmname-successfully-stopped": "Virtual machine {0} successfully stopped!", "are-you-sure-you-want-to-stop-virtual-machine-item-vmname": "Are you sure you want to stop virtual machine {0} ?", "stop-virtual-machine": "Stop Virtual machine", "vm-item-vmname-is-successfully-deleted": "VM {0} is successfully deleted!", "please-confirm-deleting-virtual-machine-item-vmname": "Please confirm deleting virtual machine {0} :", "storage-gib": "Storage (GiB)", "start": "Start", "stop": "Stop", "time-range-for-start-greater-than-end-can-be-maximum-1-month": "Time range for start -> end can be maximum 1 month", "total": "Total", "virtual-gpu-unit": "Virtual GPU unit", "public-ip-unit": "Public IP unit", "flush-storage-unit": "Flush storage unit", "windows-unit": "Windows Unit", "public-ip-address-unit": "Public IP Address Unit", "memory-unit": "Memory Unit", "virtual-cpu-unit": "Virtual CPU Unit", "network-unit": "Network Unit", "transaction-unit": "Transaction Unit", "storage-unit": "Storage Unit", "please-note-that-the-values-displayed-in-the-spendings-graph-depend-on-the-current-prices-of-the-selected-location-location-if-the-prices-have-changed-during-the-selected-period-the-graph-will-only-consider-the-latest-prices-that-were-set": "Please note that the values displayed in the spendings graph depend on the current prices of the selected location ({0}). If the prices have changed during the selected period, the graph will only consider the latest prices that were set.", "network-usage": "Network usage", "usage": "Usage", "mac-address-network-nic_mac": "Mac address: {0}", "network-interface-network-nic_name": "Network interface: {0}", "amount-of-storage-being-written": "Amount of storage being written", "vdisk-bandwidth": "vDisk Bandwidth", "disk-input-output-operations-per-second": "Disk input/output operations per second.", "vdisk-iops": "vDisk iops", "used-memory-size": "Used memory size", "used-cpu-time": "Used CPU time", "boot-type": "Boot type", "cpu-pinning-enables-applications-to-exclusively-bind-or-unbind-a-virtual-cpus-of-your-vm-to-physical-cores-to-improve-performance": "CPU pinning enables applications to exclusively bind or unbind a virtual CPUs of your VM to physical cores to improve performance", "cpu-pinning": "CPU pinning", "agent": "Agent", "due-to-cpu-topology-minimum-number-of-vcpus-should-be-vm-cpu_topology-cores-vm-cpu_topology-threads-and-you-can-increase-decrease-vcpus-by-vm-cpu_topology-cores-vm-cpu_topology-threads": "Due to CPU topology, minimum number of vCPUs should be {0}, and you can increase / decrease vCPUs by {1}", "sockets-vm-cpu_topology-sockets": "Sockets : {0}", "threads-vm-cpu_topology-threads": "Threads : {0}", "cores-vm-cpu_topology-cores": "Cores : {0}", "cpu-topology": "CPU topology", "create-template": "Create Template", "attach-cdrom-image": "Attach cdrom image", "attach-nic": "Attach NIC", "add-disk": "Add disk", "console": "<PERSON><PERSON><PERSON>", "reboot": "Reboot", "pause": "Pause", "resume": "Resume", "detach-selected": "<PERSON><PERSON> selected", "software-licenses": "Software licenses", "licenses-count": "Number of licenses", "spendings": "Spendings", "statistics": "Statistics", "os-image": "OS image", "initial-password": "Initial password", "virtual-machine-vcpus-updated-successfully": "Virtual machine VCPUs updated successfully!", "updating-the-vm-vcpus-will-change-the-vm-price-from-this-totalprice-this-currency-to-newprice-price-this-currency": "Updating the vm VCPUs will change the vm price from {0} {1} to {2} {3}", "updating-vm-vcpus": "Updating vm VCPUs", "virtual-machine-memory-updated-successfully": "Virtual machine memory updated successfully!", "updating-the-vm-memory-will-change-the-vm-price-from-this-totalprice-this-currency-to-newprice-price-this-currency": "Updating the vm memory will change the vm price from {0} {1} to {2} {3}", "updating-vm-memory": "Updating vm memory", "selected-disks-are-detached-successfully": "Selected disks are detached successfully!", "please-confirm-detaching-selected-disks-note-that-boot-disks-wont-be-detached": "Please confirm detaching selected disks. Note that BOOT disks won't be detached.", "selected-disk-are-deleted-successfully": "Selected disk are deleted successfully!", "please-confirm-deleting-selected-disks-note-that-boot-disks-wont-be-deleted": "Please confirm deleting selected disks. Note that BOOT disks won't be deleted.", "delete-disks": "Delete disks", "vcpus-successfully-unpinned-from-physical-cpu": "vcpus successfully  'unpinned from' physical cpu", "unpinning-vcpus-from-physical-cpus-will-reduce-the-cost-of-each-vcpu-by-4-times-nnew-vm-price-will-be-newprice-tofixed-2-this-currency": "Unpinning vcpus from physical CPUs will reduce the cost of each vCPU by 4 times. New vm price will be: {0} {1}", "unpinning-cpus": "Unpinning CPUS", "pinning-vcpus-to-physical-cpus-will-cost-4x-of-the-original-vcpu-unit-price-nnew-vm-price-will-be-newprice-tofixed-2-this-currency": "Pinning vcpus to physical cpus will cost 4x of the original vcpu unit price. New vm price will be: {0} {1}", "pinning-cpus": "Pinning CPUS", "cputopology": "CPUTopology", "vm-this-vm-name-successfully-rebooted": "VM {0} successfully rebooted!", "are-you-sure-you-want-to-reboot-vm-this-vm-name": "Are you sure you want to Reboot vm {0} ?", "reboot-vm": "Reboot VM", "vm-this-vm-name-successfully-resumed": "VM {0} successfully resumed!", "are-you-sure-you-want-to-resume-vm-this-vm-name": "Are you sure you want to Resume vm {0} ?", "resume-vm": "Resume VM", "vm-this-vm-name-successfully-paused": "VM {0} successfully Paused!", "are-you-sure-you-want-to-pause-vm-this-vm-name": "Are you sure you want to Pause vm {0} ?", "pause-vm": "Pause VM", "vm-this-vm-name-successfully-started": "VM {0} successfully started!", "are-you-sure-you-want-to-start-vm-this-vm-name": "Are you sure you want to Start vm {0} ?", "start-vm": "Start VM", "force-stop-this-vm": "Force stop this vm.", "vm-this-vm-name-successfully-stopped": "VM {0} successfully stopped!", "are-you-sure-you-want-to-stop-vm-this-vm-name": "Are you sure you want to stop vm {0} ?", "stop-vm": "Stop VM", "the-agent-is-successfully-this-agentenabled-enabled-disabled": "The agent is successfully {0}", "please-confirm-detaching-vgpu-from-your-virtual-machine": "Please confirm detaching vGPU from your virtual machine:", "vgpu-is-detached-successfully": "vGPU is detached successfully!", "please-confirm-deleting-vgpu": "Please confirm Deleting vGPU:", "vgpu-is-deleted-successfully": "vGPU is deleted successfully!", "bandwidth": "Bandwidth", "tpm-description": "Attach a TPM device", "tpm-added": "TPM device added successfully", "tpm-removed": "TPM device detached successfully", "tpm-adding-title": "Attaching TPM device", "tpm-removing-title": "Detaching TPM device", "tpm-adding-msg": "Add TPM device to virtual machine", "tpm-removing-msg": "Detaching the TPM device will permanently delete its state and all associated keys. This action cannot be undone.", "tpm-secret": "TPM password", "cant-update-tpm": "Cannot enable/disable TPM for a running VM, please stop the VM and try again.", "tpm-secret-title": "TPM password required.", "tpm-secret-message": "This VM has TPM enabled. To start it, you must enter a password to encrypt the TPM state for extra security.  We do not store this password. If lost, the TPM state and all stored keys will be  permanently unrecoverable. ", "security": "Security", "tpm-secret-required": "TPM password is required", "bacukp-policy-required": "Your cloud admin requires you to select a backup policy before creating a VM. Please select a backup policy.", "updating-backup-settings": "Updating backup settings", "updating-backup-settings-warning": "If you require VM backup policies, you must have atleast one policy defined or allow backup override. Do you want to continue?"}, "kubernetes": {"please-confirm-changing-node-pool-count": "Please confirm {0} node pool node count to {1}", "increasing": "increasing", "decreasing": "decreasing", "node-pools": "Node pools", "kubernetes-clusters": "Kubernetes clusters", "management-clusters": "Management clusters", "rancher": "Rancher", "rancher-version": "Rancher version", "supported-kubernetes-versions": "Supported kubernetes versions", "kubernetes": "Kubernetes", "node-pool-data-name-added-successfully": "Node pool {0} added successfully", "pin-vcpus-pinned-vcpus-are-charged-4-vcu-per-month-while-unpinned-vcpus-are-charged-1-vcu-per-month": "Pin vCPUs (Pinned vCPUs are charged 4 VCU per month while unpinned vCPUs are charged 1 VCU per month.)", "add-more-than-1000-node-count": "Add more than 1000 node count", "node-count": "Node count", "master-node": "Master node", "enter-node-pool-name": "Enter node pool name", "add-node-pool": "Add node pool", "please-provide-cluster-name-first": "Please provide cluster name first", "please-provide-node-parseint-i-1-name": "Please provide node {0} name!", "you-must-configure-at-least-one-worker-node-pool": "You must configure at least one worker node pool!", "please-provide-node-pool-parseint-i-1-name": "Please provide node pool {0} name!", "confirm-deleting-node-pool-by-typing-name": "To confirm your action, type ", "below": "in the field below", "you-must-configure-at-least-one-master-node": "You must configure at least one master node!", "kubernetes-version": "Kubernetes version", "worker-node-pools": "Worker node pools", "configure-the-way-how-you-want-to-deploy-your-kubernetes-worker-nodes-if-you-deploy-the-master-and-worker-nodes-in-different-cloudspaces-you-need-to-make-sure-that-they-have-connectivity-to-each-other-you-can-use-the-connected-cloudspaces-feature-if-you-used-the-default-builtin-firewall-in-case-you-chose-to-use-custom-firewalls-setup-vpn-tunnels-between-the-cloudspaces-that-will-run-the-master-and-worker-nodes-the-same-applies-to-the-connectivity-in-which-the-rancher-management-is-deployed": "Configure the way how you want to deploy your kubernetes worker nodes. If you deploy the master and worker nodes in different cloudspaces, you need to make sure that they have connectivity to each other. You can use the \"connected cloudspaces\" feature if you used the default builtin firewall. In case you chose to use custom firewalls, setup vpn tunnels between the cloudspaces that will run the master and worker nodes. The same applies to the connectivity in which the Rancher management is deployed.", "master-node-pools": "Master node pools", "configure-cluster-name": "Configure cluster name", "configure-worker-node-pools": "Configure worker node pools", "configure-master-node-pools": "Configure master node pools", "kubernetes-cluster-this-cluster-name-is-being-deleted": "Kubernetes cluster {0} is being deleted!", "please-confirm-deleting-kubernetes-cluster-this-cluster-name": "Please confirm deleting kubernetes cluster {0}:", "rancher-management-cluster-is-scheduled-to-delete-successfully": "Rancher management cluster is scheduled to delete successfully!", "please-confirm-deleting-management-cluster-this-cluster-name": "Please confirm deleting management cluster {0}:", "delete-kubernetes-cluster": "Delete kubernetes cluster", "server-pool": "Server pool", "load-balancers": "Load balancers", "external-network-ip": "External network IP", "external-network-id": "External network ID", "ingress-resources-will-be-deleted-by-deleting-cluster": "Ingress resources will be deleted by deleting cluster:", "therere-no-dns-records-connected-to-resources-in": "There're no DNS records connected to resources in", "dns-records-will-be-deleted-by-deleting-cluster": "DNS records will be deleted by deleting cluster:", "virtual-machines-will-be-deleted-by-deleting-cluster": "Virtual machines will be deleted by deleting cluster:", "kubernetes-clusters-will-be-deleted-by-deleting-cluster": "Kubernetes clusters will be deleted by deleting cluster:", "deleting-a-boolean-kubernetesclusterid-kubernetes-management-cluster-will-delete-all-resources-connected-to-it": "Deleting a {0} cluster will delete all resources connected to it!", "kube-config": "<PERSON><PERSON> config", "cluster-management": "Cluster management", "create-node-pool": "Create Node pool", "provider": "Provider", "something-went-wrong": "Something went wrong", "kube-config-will-be-available-when-the-cluster-is-deployed-and-running": "Kube config will be available when the cluster is deployed and running", "version": "Version", "kubernetes-cluster": "Kubernetes cluster", "node-pool-is-scaled-successfully": "Node pool is scaled successfully!", "updating-the-node-pool-node-count-will-action-the-node-pool-price-by-this-nodeprice-nodesdifference-tofixed-2-this-currency": "Updating the node pool node count will {0} the node pool price by {1} {2}.", "updating-node-count": "Updating Node count", "node-pool-this-items-0-value-is-deleted-successfully": "Node pool {0} is deleted successfully!", "please-confirm-deleting-node-pool-this-items-0-value": "Please confirm deleting node pool {0} :", "delete-node-pool": "Delete node pool", "load-balancer-ip": "Load balancer IP", "ssl-issuer": "SSL issuer", "ingress-controller-status": "Ingress controller status", "ingress-class-name": "Ingress class name", "hostname-prefix": "Hostname prefix", "driver": "Driver", "nodes": "Nodes", "node-name": "Node name", "pending": "Pending", "node-pool-updated-successfully": "Node pool updated successfully", "node-pool": "Node pool", "scale-node-pool": "Scale node pool", "node-pools-will-be-available-when-the-cluster-is-deployed-and-running": "Node pools will be available when the cluster is deployed and running", "scale": "Scale", "more-than-1000": "More than 1000", "new-size": "New size", "select-top-level-domain": "Select top level domain", "management-cluster-is-being-deployed": "Management cluster is being deployed!", "please-provide-missing-fields-first": "Please provide missing fields first!", "highly-available-deploy-your-rancher-management-cluster-on-three-virtual-machines-grouped-in-anti-affinity-group": "Highly available (Deploy your rancher management cluster on three virtual machines grouped in anti-affinity-group)", "single-deploy-your-rancher-management-cluster-on-one-virtual-machine": "Single (Deploy your rancher management cluster on one virtual machine)", "configure-your-cluster-type": "Configure your cluster type", "certificate-store-use-a-valid-certificate-configured-on-our-portal": "Certificate store (use a valid certificate configured on our portal)", "lets-encrypt-email": "Lets encrypt email *", "using-letsencrypt-youll-need-to-provide-your-letsencrypt-email-to-automatically-generate-the-certificate": "Using LETSENCRYPT (You'll need to provide your LETSENCRYPT email to automatically generate the certificate)", "configure-your-certificate": "Configure your certificate", "select-the-domain-to-be-used-with-the-selectedexternal-network-ip": "Select the domain to be used with the selectedexternal network ip", "available-top-level-domains": "Available top level domains *", "provide-your-domain-must-point-to-the-selected-external-network-ip": "Provide your domain (Must point to the selected external network ip)", "your-own-domain": "Your own domain", "create-new-domain": "Create new domain", "add-your-domain-configuration": "Add your domain configuration", "select-external-network-ip-to-host-the-management-cluster-dashboard": "Select external network ip to host the management cluster dashboard", "select-a-cloudspace-to-deploy-the-management-cluster-nodes-in": "Select a cloudspace to deploy the management cluster nodes in", "provide-a-name-for-the-management-cluster": "Provide a name for the management cluster", "cluster-information": "Cluster information", "domain-configuration": "Domain configuration", "end-at": "End at", "final-state": "Final state", "current-state": "Current state", "start-at": "Start at", "from-state": "From state", "task-id": "Task ID", "workflows": "Workflows", "end-of-execution": "End of execution", "transitions": "Transitions", "logs": "Logs", "duration": "Duration", "cluster-status": "Cluster status", "creation-logs-of": "Creation logs of", "vm-vm-vm_name-successfully-rebooted": "VM {0} successfully rebooted!", "are-you-sure-you-want-to-reboot-vm-vm-vm_name": "Are you sure you want to Reboot vm {0} ?", "cluster": "Cluster", "health": "Health", "node": "Node", "provisioned-nodes": "Provisioned nodes", "cluster-nodes": "Cluster nodes", "vacuum": "Vacuum", "create-kubernetes-cluster": "Create Kubernetes cluster", "cluster-creation-logs": "Cluster creation logs", "ssl-certificate-source": "SSL certificate source", "rancher-console": "Rancher console", "selected-clusters-are-deleted-successfully": "Selected clusters are deleted successfully", "delete-selected-clusters": "Delete selected clusters", "create-management-cluster": "Create management cluster", "cluster-console": "Cluster console", "rancher-management-clusters-allow-you-to-create-and-maintain-kubernetes-clusters": "Rancher management clusters allow you to create and maintain kubernetes clusters", "vm-vm-vmname-successfully-rebooted": "VM {0} successfully rebooted!", "are-you-sure-you-want-to-reboot-vm-vm-vmname": "Are you sure you want to Reboot vm {0} ?", "kubernetesdsfsdf": "Kubernetesdsfsdf", "vacuum-selected-nodes": "Vacuum Selected nodes", "management-cluster-is-vacuumed-successfully": "Management cluster is vacuumed successfully", "please-confirm-vacuuming-management-cluster-note-that-vacuuming-the-management-cluster-will-delete-all-selected-master-worker-nodes": "Please confirm vacuuming management cluster. Note that vacuuming the management cluster will delete all selected master / worker nodes.", "vacuum-rancher-management-cluster": "Vacuum rancher management cluster", "selected-nodes-will-be-deleted-and-can-no-longer-be-restored": "Selected nodes will be deleted and can no longer be restored.", "kube-config-is-copied-to-clipboard": "<PERSON>be config is copied to clipboard`", "create-cluster-message": "Configure the way how you want to deploy your kubernetes master nodes. If you deploy the master nodes in different cloudspaces, you need to make sure that they have connectivity to each other. You can use the \"connected cloudspaces\" feature if you used the default builtin firewall. In case you chose to use custom firewalls, setup vpn tunnels between the cloudspaces that will run the master nodes. The same applies to the connectivity in which the Rancher management is deployed.", "please-confirm-deleting-selected-clusters": "Please confirm deleting selected clusters", "image-updated-successfully": "Image updated successfully", "update-rancher-image": "update rancher image", "cluster-invalid-name": "The cluster name can only contain lowercase, digits and hyphens!", "upgrading-rancher-version": "Upgrading rancher version!"}, "vco": {"backup-size-limit-description": "Adjusts how much disk data is grouped per backup — choose based on workload type", "backup-block-size-description": "Determines how your disk data is divided into chunks during backup operations", "backup-block-size": "Backup block size", "block-size-must-be-a-multiple-of-4": "Block size must be a multiple of 4'", "disk-backup-block-size": "Disk backup block size in KB", "not-set": "Not set", "backup-size-limit-optional": "Backup size limit (optional)", "backup-size-limit": "Backup size limit", "raid1": "raid1", "raid5": "raid5", "create-physical-storage-disk": "Create physical storage disk", "create-software-disk": "Create software disk", "choose-disk-type": "Choose disk type", "no-raid": "No raid", "physical-storage-flash-based-volume-local-to-the-hypervisor": "Physical storage (Flash based volume local to the hypervisor)", "software-defined-storage-standard-snapshots-redundant-across-multiple-servers-and-storage-devices": "Software defined storage (Standard, snapshots, redundant across multiple servers and storage devices)", "virtual-machine-mobility-disks-created-as-physical-storage-are-physically-attached-to-a-specific-node-this-means-that-virtual-machines-using-physical-storage-disks-cannot-be-moved-to-other-physical-nodes-during-maintenance-activities-or-node-failures-if-node-mobility-is-a-critical-requirement-for-your-virtual-machines-consider-using-software-defined-storage-solutions-instead": "Virtual Machine Mobility: Disks created as physical storage are physically attached to a specific node. This means that virtual machines using physical storage disks cannot be moved to other physical nodes during maintenance activities or node failures. If node mobility is a critical requirement for your virtual machines, consider using software defined storage solutions instead.", "physical-storage": "Physical storage", "external-networks": "external networks", "this-this-record_type-record-already-exists-for-this-domain-if-several-hosts-are-configured-with-the-same-domain-round-robin-scheduling-will-be-applied-to-distribute-incoming-requests-for-this-domain-among-the-hosts-are-you-sure-you-want-to-proceed": "This {0} record already exists for this domain. If several hosts are configured with the same domain, Round Robin scheduling will be applied to distribute incoming requests for this domain among the hosts. Are you sure you want to proceed?", "a-top-level-domain-is-a-domain-name-to-and-under-which-the-system-can-create-domain-name-records-dns-records-for-the-external-ip-addresses-of-your-cloud-resources-cloudspaces-objectspaces-virtual-machines": "A top level domain is a domain name to and under which the system can create domain name records (DNS records) for the external IP addresses of your cloud resources (cloudspaces / objectspaces / virtual machines).", "add-domain-to-external-network": "Add domain to external network", "enable-customer": "Enable customer", "delete-customer": "Delete customer", "please-enter-iam-organization-first": "please enter IAM organization first", "approve": "Approve", "create-customer": "Create Customer", "organization": "Organization *", "access-info": "Access Info", "vat-number-optional": "VAT Number (optional)", "billing-address-optional": "Billing address (optional)", "billing-contact-phone-number-optional": "Billing contact phone number (optional)", "billing-contact-email-optional": "Billing contact email (optional)", "show-price-information": "show price information", "billable-if-active-this-customer-will-pay-for-service": "Billable (If active, this customer will pay for service)", "billing": "Billing", "contact-phone": "Contact Phone *", "contact-email": "Contact Email *", "contact-name": "Contact name *", "grant-the-customer-access-to-cloud-locations": "Grant the customer access to cloud locations.", "step-four": "Step four:", "fill-this-form-to-create-the-customer": "Fill this form to create the customer.", "step-three": "Step three:", "your-customer-will-most-likely-have-more-than-one-person-to-administer-the-cloud-resources-this-is-why-a-customer-is-linked-to-an-organization-in-the-iam-and-not-to-a-single-person-for-security-reasons-the-customer-needs-to-create-the-organization-in-the-iam-himself-so-that-he-is-the-owner-so-before-filling-this-form-ask-the-customer-contact-to-create-his-organization-in-the-iam": "Your customer will most likely have more than one person to administer the cloud resources. This is why a customer is linked to an organization in the IAM and not to a single person. For security reasons the customer needs to create the organization in the IAM himself so that he is the owner. So before filling this form, ask the customer contact to create his organization in the IAM.", "customer-organization-in-the": "Customer organization in the", "step-two": "Step two:", "identifying-access-to-customer-resources-happens-through-the-identity-and-access-manager-so-before-filling-this-form-ask-the-customer-contact-to-register-his-user-account-in": "Identifying access to customer resources happens through the identity and access manager. So before filling this form, ask the customer contact to register his user account in", "identity-and-access-manager": "identity and access manager", "customer-sign-up-in-the": "Customer sign up in the", "step-one": "Step one:", "onboarding-customers": "Onboarding customers", "location-this-selectedlocation-name-added-successfully": "Location {0} added successfully!", "you-must-select-location-first": "You must select location first!", "city": "City", "country": "Country", "datacenter-name": "Datacenter name", "datacenter-code": "Datacenter code", "this-customer-is-using": "This customer is using", "in-this-page-you-can-define-the-customers-prices-for-this-location": "In this page you can define the customer's prices for this location.", "you-can-change-customer-pricing-or-configure-this-customer-to-use-standard-prices": "You can change customer pricing or configure this customer to use standard prices", "customer-prices": "Customer Prices", "location-prices": "Location prices", "successfully-rescanned-vm": "Successfully rescanned VM", "license-compliance": "License compliance", "blog-deleted-successfully": "Blog deleted successfully", "please-confirm-deleting-notification-blog-item-title": "Please confirm deleting notification blog {0}", "maintenance-status-enumtotext-item-maintenance_status": "Maintenance status: {0}", "reason-item-reason": "Reason: {0}", "till-timestamptodate-item-till_time": "Till: {0}", "from-timestamptodate-item-from_time": "From: {0}", "this-blog-is-created-and-maintained-by-item-sender": "This blog is created and maintained by {0}", "author-item-sender": "Author: {0}", "news-featured-announcements-and-software-updates": "News, featured announcements and software updates", "containerspaces-allow-you-to-deploy-and-manage-kubernetes-clusters": "Containerspaces allow you to deploy and manage Kubernetes clusters.", "containerspaces": "Containerspaces", "my-objectspaces": "My Objectspaces", "objectspaces-allow-you-to-make-s3-endpoints-available-to-your-cloudspaces-and-if-needed-also-externally-to-the-internet-or-your-internal-company-network": "Objectspaces allow you to make S3 endpoints available to your cloudspaces and if needed also externally to the internet or your internal company network.", "create-new": "Create New", "my-cloudspaces": "My Cloudspaces", "cloudspaces-allow-to-deploy-compute-resources-into-a-private-network-that-is-optionally-connected-to-an-external-network-such-as-eg-the-internet-or-directly-to-one-of-your-internal-company-networks": "Cloudspaces allow to deploy compute resources into a private network that is optionally connected to an external network such as eg the internet or directly to one of your internal company networks.", "successfully-reset-prices-to-standard-prices": "successfully reset prices to standard prices", "location-location-removed-successfully": "Location {0} removed successfully!", "are-you-sure-you-want-to-make-location-location-unavailable-for-customer-this-activecustomer": "Are you sure you want to make location {0} unavailable for customer {1} ?", "remove-location": "Remove location", "reset-to-standard-prices": "Reset to standard prices", "add-location": "Add location", "conversion-rate-1-convertedcurrency-oldval": "Conversion rate 1 {0} =", "conver-prices-from-convertedcurrency-oldval-to-convertedcurrency-newval": "Conver prices from {0} to {1}?", "bill-gates": "<PERSON>", "due-date": "Due Date", "invoice-date": "Invoice Date", "customer-name": "Customer Name", "not-found": "Not Found", "screen-shows-your-invoices-clicking-on-invoice-number-will-open-a-new-tab-containing-full-invoice-details-pdf": "screen shows your invoices.Clicking on invoice number will open a new tab containing full invoice details pdf.", "screen-allows-to-control-in-which-locations-the-customer-will-be-able-to-deploy-resources-and-allows-adding-custom-prices-for-this-customer-updating-prices-will-not-affect-other-customers": "screen allows to control in which locations the customer will be able to deploy resources and allows adding custom prices for this customer. Updating prices will not affect other customers.", "screen-allows-maintaining-customer-contact-and-billing-details": "screen allows maintaining customer contact and billing details.", "basic-info": "Basic Info", "customer-management": "Customer Management", "customer-item-customer_id-restored-successfully": "Customer {0} restored successfully!", "customer-item-customer_id-approved-successfully": "Customer {0} approved successfully!", "all-the-selected-customers-resources-will-be-deleted-to-the-recycle-bin-after-7-days-these-resources-will-be-permanently-deleted-please-confirm-deleting-selected-customers": "All the selected customers' resources will be deleted to the recycle bin. After 7 days these resources will be permanently deleted. Please confirm deleting selected customers:", "customer-item-name-is-deleted-successfully": "Customer {0} is deleted successfully.", "all-the-customer-resources-will-be-deleted-to-the-recycle-bin-after-7-days-these-resources-will-be-permanently-deleted-please-confirm-deleting-customer-item-name": "All the customer resources will be deleted to the recycle bin. After 7 days these resources will be permanently deleted. Please confirm deleting customer {0} :", "new-customer-account": "New Customer account", "delete-selected-customers": "Delete selected customers", "billable": "Billable", "company-name": "Company name", "include-deleted": "Include deleted", "disk-successfully-attached-to-this-vmname": "Disk Successfully attached to {0}", "select-vm-select-cloudspace-first": "select VM (select cloudspace first)", "virtual-machines": "Virtual machines *", "cloudspaces": "Cloudspaces *", "disk-created-successfully": "<PERSON>sk created successfully!", "please-enter-disk-name-first": "Please enter disk name first!", "create-disk-form-snapshot": "Create disk form snapshot", "set-disk-parameters": "Set disk parameters", "create-empty-disk": "Create empty disk", "disk-data": "Disk data", "choose-disk-origin": "Choose disk origin", "disk-id": "Disk id", "select-disk": "Select disk:", "speed-iops": "Speed (IOPS)", "disk-size-gib": "Disk Size (GiB)", "description-for-disk": "Description for disk", "write-a-unique-name-for-the-disk": "Write a unique name for the disk", "disk-name": "Disk name *", "create-disk-from-a-snapshot": "Create disk from a snapshot", "create-an-empty-disk": "Create an empty disk", "attach-disk": "Attach disk", "snapshot-created-successfully": "Snapshot created successfully!", "enter-snapshot-name-first": "Enter snapshot name first!", "snapshots": "Snapshots", "write-a-name-for-snapshot": "Write a name for snapshot", "snapshot-name": "Snapshot name *", "iops-updated-successfully": "Iops updated successfully!", "updating-the-disk-speed-will-change-the-vm-price-from-this-totalprice-this-currency-to-newprice-price-this-currency": "Updating the disk speed will change the vm price from {0} {1} to {2} {3}", "updating-disk-speed": "Updating disk speed", "disk-speed-updated-successfully": "Disk speed updated successfully.", "updating-the-disk-size-will-change-the-vm-price-from-this-totalprice-this-currency-to-newprice-price-this-currency": "Updating the disk size will change the vm price from {0} {1} to {2} {3}", "updating-disk-size": "Updating disk size", "disk-size-updated-successfully": "Disk size updated successfully.", "disk-this-data-disk_name-successfully-detached-from-this-virtual-machine": "Disk {0} successfully detached from this virtual machine!", "are-you-sure-you-want-to-detach-this-data-disk_name-from-this-virtual-machine": "Are you sure you want to detach {0} from this virtual machine ?", "successfully-delete-disk-this-data-disk_name": "Successfully delete disk {0}!", "please-confirm-deleting-disk-this-data-disk_name": "Please confirm deleting disk {0} :", "cache-settings-beta": "Cache settings (BETA)", "take-snapshot": "Take snapshot", "exposed": "Exposed", "selected-disks-are-deleted-successfully": "Selected disks are deleted successfully!", "please-confirm-deleting-selected-disks-please-note-that-boot-disks-wont-be-deleted": "Please confirm deleting selected disks. Please note that BOOT disks won't be deleted :", "disk-item-disk_name-is-successfully-detached-from-virtual-machine-item-vm_name": "Disk {0} is successfully detached from virtual machine {1}!", "please-confirm-detaching-disk-item-disk_name-from-virtual-machine-item-vm_name": "Please confirm detaching disk {0} from virtual machine {1} :", "disk-item-disk_name-is-successfully-deleted": "Disk {0} is successfully deleted!", "please-confirm-deleting-disk-item-disk_name": "Please confirm deleting disk {0} :", "your-customer-this-activecustomer-does-not-have-access-to-any-locations-contact-your-administrator-to-request-the-access": "Your customer {0} does not have access to any locations. Contact your administrator to request the access.", "create-disk": "Create Disk", "detach-from-item-vm_name": "Detach from {0}", "disk-cache-updated-successfully": "Disk cache updated successfully", "before-proceeding-with-the-disk-cache-configuration-please-ensure-you-have-read-the-documentation-and-are-aware-of-the-risks-involved-do-you-want-to-continue": "Before proceeding with the disk cache configuration, please ensure you have read the documentation and are aware of the risks involved. Do you want to continue?", "disk-cache-configuration": "Disk Cache Configuration", "please-fill-all-required-fields": "Please fill all required fields.", "set": "Set", "wait": "Wait", "restart": "<PERSON><PERSON>", "write-back-cache-mode-failure-policy": "Write back cache mode failure policy", "cache-mode": "Cache mode", "cache-size-gib": "<PERSON><PERSON> GiB", "the-cache-evicts-the-least-recently-used-blocks-first": ") The cache evicts the least recently used blocks first.", "least-recently-used": "Least recently used (", "the-cache-evicts-the-blocks-in-the-order-they-were-added-without-any-regard-to-how-often-or-how-many-times-they-were-accessed-before": ") The cache evicts the blocks in the order they were added, without any regard to how often or how many times they were accessed before.", "first-in-first-out": "First in first out (", "the-cache-randomly-selects-a-candidate-block-and-discards-it-to-make-space-when-necessary": ") The cache randomly selects a candidate block and discards it to make space when necessary.", "random": "Random", "random-replacement": "Random replacement (", "when-the-cache-is-full-the-replacement-policy-algorithm-must-choose-which-blocks-to-discard-to-make-room-for-the-new-ones": "When the cache is full, the replacement policy algorithm must choose which blocks to discard to make room for the new ones.", "replacement-policy": "Replacement policy", "wait-until-the-hypervisor-recovers-and-only-then-restart-the-vm": "Wait until the hypervisor recovers and only then restart the VM.", "restart-the-vm-on-another-hypervisor-with-the-risk-of-dataloss": "Restart the VM on another hypervisor with the risk of dataloss.", "the-failure-policy-specifies-what-happens-in-case-of-a-hypervisor-outage-this-is-only-relevant-in-case-of-using-writeback-cache-mode": "The failure policy specifies what happens in case of a hypervisor outage. This is only relevant in case of using WRITEBACK cache mode", "failure-policy": "Failure policy", "cache-is-a-mode-where-the-disk-writes-are-sent-directly-to-the-disk-instead-of-storing-the-data-in-the-cache-first-and-then-writing-it-to-the-disk-later-the-first-time-this-data-is-read-it-will-be-read-from-the-disk-one-of-the-advantages-of-this-is-to-have-more-space-for-caching-reads-because-the-cache-is-not-filled-with-data-that-may-not-be-read-soon-and-the-cache-space-is-reserved-for-more-frequently-accessed-data": "cache is a mode where the disk writes are sent directly to the disk instead of storing the data in the cache first and then writing it to the disk later. The first time this data is read it will be read from the disk. One of the advantages of this is to have more space for caching reads because the cache is not filled with data that may not be read soon, and the cache space is reserved for more frequently accessed data.", "write-around": "Write Around", "cache-is-a-mode-where-the-disk-writes-are-sent-both-to-the-cache-and-the-disk-at-the-same-time-and-an-acknowledgment-is-sent-to-the-host-system-only-after-the-data-is-written-to-the-disks-this-means-that-write-operations-are-slower-and-the-host-system-has-to-wait-longer-however-this-also-means-that-there-is-no-risk-of-data-loss-or-data-corruption-if-the-cache-fails-or-if-there-is-a-power-outage-as-the-data-is-already-on-the-disks": "cache is a mode where the disk writes are sent both to the cache and the disk at the same time and an acknowledgment is sent to the host system only after the data is written to the disks. This means that write operations are slower and the host system has to wait longer, however this also means that there is no risk of data loss or data corruption if the cache fails or if there is a power outage, as the data is already on the disks.", "write-through": "Write Through", "cache-is-a-mode-where-the-disk-writes-are-sent-to-the-cache-and-immediately-an-acknowledgment-is-sent-to-the-host-system-without-waiting-for-the-data-to-be-written-to-the-disk-this-means-that-the-write-operations-are-faster-however-this-also-means-that-there-is-a-risk-of-data-loss-or-corruption-if-the-cache-fails-or-in-case-of-power-outage-before-data-is-flushed-to-the-disks": "cache is a mode where the disk writes are sent to the cache and immediately an acknowledgment is sent to the host system, without waiting for the data to be written to the disk. This means that the write operations are faster, however this also means that there is a risk of data loss or corruption if the cache fails or in case of power outage before data is flushed to the disks.", "write-back": "Write Back", "cache-modes": "Cache modes", "amount-in-gib-to-use-for-cache-shouldnt-exceed-the-actual-size-of-the-disk": "Amount in GiB to use for cache, Shouldn't exceed the actual size of the disk", "cache-size": "Cache size:", "a-disk-cache-is-a-cache-memory-that-is-used-to-speed-up-the-process-of-storing-and-accessing-data-from-the-hard-disk": "A disk cache is a cache memory that is used to speed up the process of storing and accessing data from the hard disk.", "disk-cache": "Disk cache:", "setting-disk-cache-this-feature-is-in-beta-testing-phase": "Setting disk cache (This feature is in beta testing phase)", "rollback-snapshot-this-snapinfo-snapshot_name-on-the-other-virtual-machine-disks-as-well": "Rollback snapshot: {0} on the other virtual machine disks as well", "successfully-rollback-to-this-snapinfo-snapshot_name": "Successfully rollback to {0}", "are-you-sure-you-want-to-rollback-to-snapshot-this-snapinfo-snapshot_name": "Are you sure you want to rollback to snapshot {0} ?", "snapshot-this-snapinfo-snapshot_name-successfully-deleted": "Snapshot {0} successfully deleted!", "please-confirm-deleting-snapshot-this-snapinfo-snapshot_name": "Please confirm deleting snapshot {0} :", "delete-snapshot": "Delete snapshot", "rollback": "Rollback", "creation-time": "Creation Time", "rollback-snapshot-item-snapshot_name-on-the-other-virtual-machine-disks-as-well": "Rollback snapshot: \"{0}\" on the other virtual machine disks as well", "successfully-rollback-to-item-snapshot_name": "Successfully rollback to {0}", "please-confirm-rolling-back-to-snapshot-item-snapshot_name": "WARNING: Rolling back to a previous snapshot may result in data loss. Any changes made since the selected snapshot was taken will be overwritten or lost. Are you sure you want to continue?", "are-you-sure-you-want-to-roll-back-to-snapshot-item-snapshot_name-this-action-might-cause-data-loss-or-corruption-when-using-writeback-cache-mode": "Are you sure you want to roll back to snapshot \"{0}\"? This action might cause data loss or corruption when using WRITEBACK cache mode.", "snapshot-item-snapshot_name-successfully-deleted": "Snapshot {0} successfully deleted!", "please-confirm-deleting-snapshot-item-snapshot_name": "Please confirm deleting snapshot {0} :", "date": "Date", "snapshots-are-not-available-on-disks-of-type-physical-disk": "Snapshots are not available on disks of type \"Physical disk\"", "external-network-with-ip-this-ipaddress-was-detached-successfully": "External network with ip {0} was detached Successfully", "item-domain_name-deleted-successfully": "{0} deleted successfully", "delete-selected-resource-dns-records": "Delete selected resource DNS records", "network-id": "Network id", "network-type": "Network type", "external-cs": "External cs", "mac-address": "Mac address", "save-view": "Save view", "show-legend": "Show legend", "export-figures": "Export figures", "dimensions-available-for-drilldown-are": "Dimensions available for drilldown are ", "spending-analysis-is-calculated-in-your-base-currency-currency": "Spending analysis is calculated in your base currency ({0}).", "spending-analysis-in-currency": "Spending analysis in ({0})", "could-not-fetch-default-currency": "Could not fetch default currency", "exchanging-between-currencies-is-based-on-the-average-exchange-rate-in-the-last-24-hours": "Exchanging between currencies is based on the average exchange rate in the last 24 hours.", "sales-analysis-is-calculated-in-your-base-currency-currency": "Sales analysis is calculated in your base currency ({0}).", "sales-analysis-in-currency": "Sales analysis in ({0})", "failed-to-load-customer-this-customerid": "Failed to load customer {0}", "customer-this-form-company_information-name-is-enabled-successfully": "Customer {0} is enabled successfully.", "are-you-sure-you-want-to-enable-customer-this-form-company_information-name": "Are you sure you want to enable customer {0} :", "customer-this-form-company_information-name-is-disabled-successfully": "Customer {0} is disabled successfully.", "are-you-sure-you-want-to-disable-customer-this-form-company_information-name": "Are you sure you want to disable customer {0} :", "customer-this-form-company_information-name-is-deleted-successfully": "Customer {0} is deleted successfully.", "all-the-customer-resources-will-be-deleted-to-the-recycle-bin-after-7-days-these-resources-will-be-permanently-deleted-please-confirm-deleting-customer-this-form-company_information-name": "All the customer resources will be deleted to the recycle bin. After 7 days these resources will be permanently deleted. Please confirm deleting customer {0} :", "customer-this-activecustomer-is-approved-successfully": "Customer {0} is approved successfully!", "customer-this-customerid-updated-successfully": "Customer {0} updated successfully!", "is-your-billing-address-err-response-data-address": "Is your billing address, {0}?", "is-your-company-address-err-response-data-address": "Is your company address, {0}?", "cloudspace.enable": "Enable", "cloudspace.disable": "Disable", "identity": "Identity", "disable-customer": "Disable customer", "external-customer-id-optional": "External customer id (optional)", "service-impact-enumtotext-item-service_impact": "Service Impact: {0}", "take-snapshots-of-all-virtual-machine-disks": "Take snapshots of all virtual machine disks"}, "others": {"default-language": "Default language", "language-updated-successfully": "Default language is updated successfully!", "go-to-home-page": "Go to home page", "access-denied": "Access Denied", "not-found": "Not Found", "notifications-preferences-updated-successfully": "Notifications preferences updated successfully", "api-tasks": "API tasks", "french": "French", "english": "English", "app": "App", "close-panel": "Close Panel", "clear-tasks-logs": "Clear Tasks Logs", "clear-selected-task": "Clear Selected Task", "ok": "Ok", "to-see-all-available-subscriptions": "to see all available subscriptions", "preferences-page": "preferences page", "go-to": "Go to", "for-all-affected-locations": "for all affected locations", "affected-locations": "Affected locations:", "new-email-subscriptions-became-available-since-your-last-login-select-your-email-subscriptions-for-userinfo-email": "New email subscriptions became available since your last login. Select your email subscriptions for {0}", "email-preferences": "Email preferences", "language": "Language", "message-item-response-data-message": "Message: {0}", "status-item-response-status-item-response-statustext": "Status: {0} {1}", "time-item-time-togmtstring": "Time: {0}", "something-went-wrong": "Something went wrong", "failed-to-get-jwt-token": "Failed to get JWT token", "accounts": "Accounts", "jwt-token": "JWT token", "copy-jwt": "Copy JWT", "explore-api": "Explore API", "get-your-jwt-via-the-button-below": "Get your JWT via the button below.", "greater-than": ">", "your-jwt": "YOUR JWT", "less-than": "<", "authorization-bearer": "Authorization: bearer", "automating-tasks-is-possible-using-the-restful-api-authenticating-your-requests-to-the-api-happens-via-the-authorization-header-in-the-http-request-e-g": "Automating tasks is possible using the RESTful API. Authenticating your requests to the API happens via the Authorization header in the http request. E.g.", "restful-api": "RESTful API", "undetermined": "Undetermined", "curl-guide": "Curl Guide", "cli-guide": "Cli Guide", "request-body": "Request Body", "request-params": "Request Params", "required": "Required", "command-is-not-supported-via-the-cli-yet": "Command is not supported via the cli yet", "3-cli-command": "3- cli Command", "2-authentication-using-jwt": "2- Authentication using JWT", "cli-installation": "CLI Installation", "follow-cli-installation-guide": "Follow CLI installation guide.", "1-installation": "1- Installation", "2-curl-command": "2- Curl Command", "authentication-is-achieved-by-using-jwt-tokens-to-access-a-valid-jwt-token-click-on-the-button-below": "Authentication is achieved by using JWT tokens. To access a valid JWT token click on the button below", "1-authentication-using-jwt": "1- Authentication using JWT", "curl-command": "cURL Command", "request-type": "Request Type", "message": "Message", "initial-time": "Initial Time", "duration": "Duration", "get-current-userjwt": "get Current UserJWT", "method-type": "Method type", "time": "Time", "logout": "Logout", "coming-soon": "Coming soon ...", "compute-unit-heat-map": "Compute unit heat map", "docs": "Docs", "compliance": "Compliance", "demo-user-is-not-configured": "Demo user is not configured", "your-payment-was-not-successful-please-try-again": "Your payment was not successful, please try again.", "your-payment-is-processing": "Your payment is processing.", "payment-succeeded": "Payment succeeded!", "an-unexpected-error-occured": "An unexpected error occured.", "attach-credit-card": "Attach credit card", "your_jwt": "YOUR_JWT", "minimum-disk-capacity-for-cloudspace-is": "Minimum disk capacity for cloudspace is", "required-actions-will-consume": "Required actions will consume", "unexpected-error": "Unexpected Error", "utilities.unauthorized": "Unauthorized", "address-is-not-clear": "Address is not clear", "this-field-is-required": "This field is required", "invalid-name": "Invalid name", "invalid-email": "Invalid email", "value-does-not-match-the-requried-value": "Value does not match the requried value", "value-does-not-match-password-field": "Value does not match password field", "invalid-url": "Invalid url", "value-length-is-too-small": "Value length is too small", "value-is-too-small": "Value is too small", "value-must-be-an-interger": "Value must be an interger", "invalid-email-address": "Invalid email address", "field-is-required": "Field is required"}, "notes": {"localization-is-live": "Localization is live!", "localization-is-live-message": "We’re excited to bring you translations in French, Dutch, and Spanish. Please bear with us, as some translations may not be perfect—our team is still working on refining them. If you spot an error, simply click 語 button to highlight all translatable text. Select the part you'd like to improve, and a suggestion box will pop up. Your feedback helps us make this experience better for everyone. Thank you for your support!"}, "user_data": {"user-data-template": "User data template", "advanced-user-data": "Advanced user data", "user-data-help-list-1-item-1": "If any templates are available in the image, you can choose one during VM creation to quickly configure your VM.", "user-data-help-list-1-item-2": "You can add custom user data to further configure your VM. You can either start from scratch or use a template as a base.", "user-data-help-list-1-item-3": "If you select a predefined template, you can edit the values of the parameters to customize them for your needs.", "user-data-help-list-1-head-2": "Adding Your Own User Data:", "user-data-help-list-1-head-3": "Editing Templates:", "user-data-help-title-1": "You can use predefined", "user-data-help-list-2-item-1": "If the parameter has a default value, it will be pre-filled for you during VM creation. You can edit it if needed.", "user-data-help-list-2-item-2": "If the parameter has no default value, you will need to provide a value .", "user-data-help-list-2-item-3": "# Default value or user input during VM creation.", "user-data-help-list-4-item-1": "Enable Advanced User Data:", "user-data-help-list-4-item-2": "To use templates or add your own user data, enable the", "user-data-help-list-4-item-3": "\"Advanced User Data\"", "user-data-help-list-4-item-4": "Select or Add Data:", "user-data-help-list-4-item-5": "option.", "user-data-help-list-4-item-6": "After enabling advanced options, you can choose a template or add your own custom user data.", "user-data-help-list-3-item-1": "Magic parameters are predefined system values that are automatically filled by the system. These parameters are not editable and do not require a default value.", "user-data-help-list-3-item-2": "Currently available magic parameters:", "user-data-help-title-2": "YAML templates with dynamic parameters for the VM configuration. You can also add your own user data or modify existing templates.", "using-templates": "Using Templates", "Image-templates-title": "User data templates", "predefined-templates": "Predefined Templates:", "types-of-parameters": "Types of Parameters", "regular-parameters": "1. Regular Parameters", "magic-parameters": "2. Magic Parameters", "how-to-use-templates": "How to Use Templates", "user-data-help-list-2-item-4": "Do not provide a default value as it will be replaced autommatically.", "the-target-cloudspace-id": "The target cloudspace id.", "the-target-cloudspace-id-on-the-g8": "The target cloudspace id on the g8.", "the-target-cloudspace-external-network-id-address": "The target cloudspace external network id address.", "the-target-external-network-ip-address": "The target external network ip address.", "the-target-cloudspace-private-network": "The target cloudspace private network.", "template-params": "Template parameters", "your-customer-id": "Your customer id.", "the-target-location": "The target location.", "your-jwt": "Your jwt.", "the-vco-domain": "The vco domain.", "the-entered-vm-name": "The entered vm name.", "user-data": "User data", "user-data-disabled": "User data (disabled)"}, "targets": {"no-subscribed-locations": "No subscribed locations was found", "please-confirm-unsubscribing-selected-locations": "Please confirm unsubscribing selected locations", "unsubscribe-locations": "Unsubscribe location(s)", "unsubscribe-selected-locations": "Unsubscribe selected locations", "must-be-at-least-8-characters-long": "Must be at least 8 characters long", "must-contain-at-least-one-special-character": "Must contain at least one special character", "must-contain-at-least-one-number": "Must contain at least one number", "must-contain-at-least-one-uppercase-letter": "Must contain at least one uppercase letter", "must-contain-at-least-one-lowercase-letter": "Must contain at least one lowercase letter", "password-is-required": "Password is required", "confirm-password": "Confirm password *", "losing-this-password-means-losing-access-to-your-backups-store-it-in-a-safe-place": "Losing this password means losing access to your backups - store it in a safe place!", "contain-at-least-one-special-character": "- Contain at least one special character", "contain-at-least-one-number": "- Contain at least one number", "contain-lowercase-and-uppercase-letters": "- Contain lowercase, and uppercase letters", "be-longer-than-8-characters": "- Be longer than 8 characters", "password-must": "Password must:", "your-restic-password-is-essential-for-backup-recovery-if-lost-your-data-cannot-be-restored": "Your Restic password is essential for backup recovery. If lost, your data cannot be restored.", "is-the-underlying-technology-used-for-backups-ensuring-secure-and-efficient-data-storage": "is the underlying technology used for backups, ensuring secure and efficient data storage.", "restic": "Restic", "restic-password-notes": "Restic Password Notes", "for-more-details": "for more details.", "s3-locking-modes-0": "S3 locking modes", "check-out": "Check out", "s3-locking-modes": "S3 locking modes", "loading-targets": "Loading targets...", "select-cloudspace": "Select Cloudspace", "s3-information-required": "S3 information (required)", "basic-information-required": "Basic information (required)", "select-cloudspace-description": "Select cloudspace (to use its network to access target)", "loading-cloudspaces": "Loading cloudspaces", "no-available-data": "No available data", "s3-details": "S3 details", "url": "URL *", "url-help": "Endpoint of S3 storage service", "region": "Region *", "region-help": "The region where the S3 bucket is located. If you're pointing to an objectspace, use region us-east-1", "access-key": "Access key *", "access-key-help": "Credentials to authenticate and access the S3 bucket. The access key must have permissions to create buckets", "secret-key": "Secret key *", "secret-key-help": "Credentials required to authenticate and access the S3 bucket", "bucket-name": "Bucket name *", "bucket-name-help": "S3 bucket where backups will be stored", "no-locking": "NO LOCKING", "governance": "GOVERNANCE", "compliance": "COMPLIANCE", "locking-mode": "Locking mode *", "locking-mode-help": "Select the locking mode for S3 storage", "basic-information": "Basic information", "name": "Name *", "restic-password": "Restic password *", "submit": "Submit", "backup-target-created-successfully": "Backup target was created successfully!", "location": "Location", "targets": "Targets", "add-target": "Add target", "loading-customers": "Loading customers", "select-customer-description": "Select customer to list cloudspaces", "location-was-unsubscribed-successfully": "location was unsubscribed successfully!", "please-confirm-unsubscribing-selected-location-item-location": "Please confirm unsubscribing selected location: {0}", "unsubscribe-location": "Unsubscribe location", "target-synced-successfully": "Target synced successfully!", "subscribe-location": "Subscribe Location", "subscribed-locations": "subscribed Locations", "sync-target": "Sync target", "unsubscribe": "unsubscribe", "backup-target-is-updated-successfully": "Backup target is updated successfully!", "location-this-selectedlocation-0-name-was-subscribed-successfully": "Location {0} was subscribed successfully", "customer-is-required": "Customer is required", "location-is-required": "Location is required", "select-location": "select Location", "objectspace.select-cloudspace": "Select cloudspace", "select-customer": "Select Customer"}, "policies": {"policies-assigned-successfully": "Backuo policies were assigned successfully!", "policy-assigned-successfully": "Backuo policy {0} was assigned successfully!", "retry-if-snapshot-timeout": "Retry if snapshot timeout", "continue-without-cooperation": "Continue without cooperation", "fail": "Fail", "your-restic-password-is-essential-for-backup-recovery-if-lost-your-data-cannot-be-restored": "Your Restic password is essential for backup recovery. If lost, your data cannot be restored.", "is-the-underlying-technology-used-for-backups-ensuring-secure-and-efficient-data-storage": "is the underlying technology used for backups, ensuring secure and efficient data storage.", "targets.restic": "Restic", "targets.restic-password-notes": "Restic Password Notes", "for-more-details": "for more details.", "targets.s3-locking-modes-0": "S3 locking modes", "targets.check-out": "Check out", "missing-required-fields": "Missing required fields", "configuration-details": "Configuration Details", "yearly": "Yearly", "monthly": "Monthly", "weekly": "Weekly", "hourly": "Hourly", "choose-number-of-snapshots-to-keep-per-each-period": "Choose number of Snapshots to Keep per each period", "restic-retention-flags": "Restic retention flags *", "use-custom-retention-flags": "Use custom retention flags", "open-cronurl-for-help-in-generating-cron-expressions": "Open {0} for help in generating cron expressions", "custom-cron-expression": "Custom Cron Expression", "use-custom-frequency": "Use custom frequency", "restic-retention-flags-details": "Restic retention flags details", "you-can-select-the-custom-option-for-more-flexible-choices-for-more-details-visit": "You can select the custom option for more flexible choices. For more details, visit:", "custom-option": "Custom option:", "meaning-the-last-5-daily-snapshots-will-be-retained-this-approach-ensures-old-backups-are-automatically-removed-while-keeping-the-most-recent-ones-based-on-the-selected-schedule": ", meaning the last 5 daily snapshots will be retained. This approach ensures old backups are automatically removed while keeping the most recent ones based on the selected schedule.", "keep-daily-5": "--keep-daily 5", "results-in-the-flag": "results in the flag", "with-a-value-of": "with a value of", "daily": "Daily", "and-specify-the-number-of-snapshots-to-keep-for-example-choosing": ") and specify the number of snapshots to keep. For example, choosing", "defines-how-snapshots-are-retained-using-restic-retention-flags-select-a-frequency-e-g": "Defines how snapshots are retained using Restic retention flags. Select a frequency (e.g.,", "configurations-details": "Configurations details"}}