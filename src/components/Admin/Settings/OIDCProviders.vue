<template>
  <page :loading="loading">
    <template #content>
      <!-- Help Section -->
      <v-card class="mb-6" variant="outlined">
        <v-card-title class="d-flex align-center">
          <v-icon class="mr-2" color="primary">mdi-help-circle</v-icon>
          {{ $t("admin.oidc.providers-help.title") }}
        </v-card-title>
        <v-card-text>
          <p>{{ $t("admin.oidc.providers-help.description") }}</p>

          <v-row class="mt-4">
            <v-col cols="12" md="6">
              <div class="text-subtitle-2 mb-2">
                <v-icon class="mr-1" size="small">mdi-shield-check</v-icon>
                {{ $t("admin.oidc.providers-help.security-title") }}
              </div>
              <p class="text-body-2">{{ $t("admin.oidc.providers-help.security-desc") }}</p>
            </v-col>
            <v-col cols="12" md="6">
              <div class="text-subtitle-2 mb-2">
                <v-icon class="mr-1" size="small">mdi-account-multiple</v-icon>
                {{ $t("admin.oidc.providers-help.management-title") }}
              </div>
              <p class="text-body-2">{{ $t("admin.oidc.providers-help.management-desc") }}</p>
            </v-col>
          </v-row>

          <div class="mt-4">
            <v-btn
              variant="outlined"
              color="primary"
              :href="$t('admin.oidc.help.docs-link')"
              target="_blank"
              prepend-icon="mdi-book-open-variant"
              size="small"
            >
              {{ $t("admin.oidc.help.view-docs") }}
            </v-btn>
          </div>
        </v-card-text>
      </v-card>

      <v-switch
        v-model="passwordLoginEnabled"
        :disabled="switchDisabled"
        @change="togglePasswordLogin"
      >
        <template v-slot:label>
          <b>
            <span>{{
              passwordLoginEnabled
                ? $t("admin.oidc.password-login-enabled")
                : $t("admin.oidc.password-login-disabled")
            }}</span>
          </b>
        </template>
      </v-switch>
      <data-table
        density="compact"
        id="providers-table"
        :loadingText="$t('admin.loading-providers')"
        :noDataText="$t('admin.no-providers-available')"
        :headers="headers"
        :items="providers"
        :loading="providersLoading"
        class="elevation-1"
        v-model:page="page"
        :limit="limit"
        @refresh="loadProviders"
      >
        <template v-slot:[`item.name`]="{ item }">
          <router-link
            :to="{ name: 'UpdateOIDCProvider', params: { id: item.id } }"
            >{{ item.name }}</router-link
          >
          <copy-id :element="{ name: item.id, id: item.id }"></copy-id>
        </template>
        <template v-slot:item.redirect="{ item }">
          <div class="d-flex align-center justify-center">
            <div class="url-text">
              {{ `${redirecturl}${item.id}` }}
            </div>
            <copy-id
              :element="{
                id: redirecturl + item.id,
                command: true
              }"
            ></copy-id>
          </div>
        </template>
        <template v-slot:item.issuer="{ item }">
          <div class="d-flex align-center justify-center">
            <div class="url-text">
              {{ item.issuer }}
            </div>
            <copy-id
              :element="{
                id: redirecturl + item.id,
                command: true
              }"
            ></copy-id>
          </div>
        </template>
        <template v-slot:item.active="{ item }">
          <v-chip :color="item.active ? 'success' : 'error'" small>
            {{
              item.active ? $t("admin.oidc.active") : $t("admin.oidc.inactive")
            }}
          </v-chip>
        </template>
        <template v-slot:[`item.actions`]="{ item }">
          <v-tooltip location="top">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                size="small"
                v-bind="props"
                :color="item.active ? 'error' : 'success'"
                @click="toggleActive(item)"
              >
                <v-icon>{{ item.active ? "mdi-close" : "mdi-check" }}</v-icon>
              </v-btn>
            </template>
            <span class="i18n" id="admin.update-status">{{
              $t("admin.update-status")
            }}</span>
          </v-tooltip>

          <v-tooltip location="top">
            <template v-slot:activator="{ props }">
              <v-btn
                variant="text"
                icon
                size="small"
                v-bind="props"
                @click="deleteItem(item)"
              >
                <v-icon>mdi-delete</v-icon>
              </v-btn>
            </template>
            <span class="i18n" id="admin.delete provider">{{
              $t("admin.delete provider")
            }}</span>
          </v-tooltip>
        </template>
      </data-table>
    </template>
  </page>
</template>

<script>
  import { PageComponent } from "../../../mixins/page"

  export default {
    name: "OIDCProviders",
    mixins: [PageComponent],
    data() {
      return {
        providersLoading: false,
        passwordLoginEnabled: true,
        passwordLoginLoading: false,
        providers: [],
        headers: [
          { text: this.$t("admin.oidc.name"), value: "name", width: "15%" },
          {
            text: this.$t("admin.oidc.issuer"),
            value: "issuer",
            align: "center"
          },
          {
            text: this.$t("admin.oidc.redirect-url"),
            value: "redirect",
            align: "center"
          },
          {
            text: this.$t("admin.oidc.status"),
            value: "active",
            align: "center",
            width: "10%"
          },
          {
            text: this.$t("admin.oidc.actions"),
            value: "actions",
            sortable: false,
            align: "center",
            width: "15%"
          }
        ],
        page: 1,
        limit: 10
      }
    },
    computed: {
      subtop() {
        return [
          {
            btn: {
              text: this.$t("admin.oidc.add-provider"),
              attrs: {
                to: { name: "CreateOIDCProvider" }
              }
            },
            icon: {
              text: "mdi-plus",
              attrs: {}
            }
          }
        ]
      },
      switchDisabled() {
        // Disable switch if there are no active OIDC providers and trying to disable password login
        if (!this.passwordLoginEnabled) return false // Already disabled, allow to enable

        // Count active providers
        const activeProviders = this.providers.filter((p) => p.active)
        return activeProviders.length === 0
      },
      redirecturl() {
        return `https://${this.iamDomain}/oidc/callback/`
      }
    },
    created() {
      this.loadProviders()
      this.getPasswordLoginStatus()
    },
    mounted() {
      this.loading = false
    },
    methods: {
      loadProviders() {
        this.providersLoading = true
        this.call(
          "listOIDCProviders",
          {},
          (data) => {
            this.providers = data.result || []
          },
          (err) => {
            this.showPopup("error", err.message)
          },
          () => {
            this.providersLoading = false
          }
        )
      },
      getPasswordLoginStatus() {
        this.passwordLoginLoading = true
        this.call(
          "getPasswordLoginStatus",
          {},
          (data) => {
            console.log(data)
            this.passwordLoginEnabled = data.enabled
          },
          (err) => {
            this.showPopup("error", err.message)
          },
          () => {
            this.passwordLoginLoading = false
          }
        )
      },
      togglePasswordLogin() {
        const action = this.passwordLoginEnabled
          ? "enablePasswordLogin"
          : "disablePasswordLogin"

        this.call(
          action,
          {},
          () => {
            this.showPopup(
              "success",
              this.$t("admin.oidc.password-login-success", [
                this.passwordLoginEnabled
                  ? this.$t("admin.oidc.enabled")
                  : this.$t("admin.oidc.disabled")
              ])
            )
          },
          (err) => {
            // Revert the switch if there's an error
            this.passwordLoginEnabled = !this.passwordLoginEnabled
            this.showPopup("error", err.message)
          }
        )
      },
      toggleActive(item) {
        this.call(
          item.active ? "deactivateOIDCProvider" : "activateOIDCProvider",
          { providerId: item.id },
          () => {
            this.showPopup(
              "success",
              this.$t("admin.oidc.provider-status-success", [
                item.active
                  ? this.$t("admin.oidc.deactivated")
                  : this.$t("admin.oidc.activated")
              ])
            )
            this.loadProviders()
            this.getPasswordLoginStatus() // Refresh password login status as it may be affected
          },
          (err) => {
            this.showPopup("error", err.message)
          }
        )
      },
      deleteItem(item) {
        this.confirm(
          this.$t("admin.oidc.delete-provider-confirm"),
          this.$t("admin.oidc.delete-provider-warning"),
          () => {
            this.call(
              "deleteOIDCProvider",
              { providerId: item.id },
              () => {
                this.showPopup(
                  "success",
                  this.$t("admin.oidc.provider-deleted-success")
                )
                this.loadProviders()
                this.getPasswordLoginStatus() // Refresh password login status as it may be affected
              },
              (err) => {
                this.showPopup("error", err.message)
              }
            )
          }
        )
      }
    }
  }
</script>

<style scoped>
  .url-text {
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
