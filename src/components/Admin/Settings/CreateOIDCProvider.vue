<template>
  <page :loading="loading">
    <template #content>
      <Breadcrumbs :items="breadcrumbs"></Breadcrumbs>

      <!-- Help Section -->
      <v-card class="mb-6" variant="outlined">
        <v-card-title class="d-flex align-center">
          <v-icon class="mr-2" color="primary">mdi-help-circle</v-icon>
          {{ $t("admin.oidc.help.title") }}
        </v-card-title>
        <v-card-text>
          <p>{{ $t("admin.oidc.help.description") }}</p>

          <v-expansion-panels class="mt-4">
            <v-expansion-panel>
              <v-expansion-panel-title>
                <v-icon class="mr-2">mdi-google</v-icon>
                {{ $t("admin.oidc.help.google-example.title") }}
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <div class="text-body-2">
                  <p><strong>{{ $t("admin.oidc.help.google-example.step1") }}</strong></p>
                  <p>{{ $t("admin.oidc.help.google-example.step1-desc") }}</p>

                  <p><strong>{{ $t("admin.oidc.help.google-example.step2") }}</strong></p>
                  <p>{{ $t("admin.oidc.help.google-example.step2-desc") }}</p>

                  <p><strong>{{ $t("admin.oidc.help.google-example.step3") }}</strong></p>
                  <ul>
                    <li><strong>{{ $t("admin.oidc.name") }}:</strong> Google</li>
                    <li><strong>{{ $t("admin.oidc.issuer-url") }}:</strong> https://accounts.google.com</li>
                    <li><strong>{{ $t("admin.oidc.client-id") }}:</strong> {{ $t("admin.oidc.help.google-example.client-id-example") }}</li>
                    <li><strong>{{ $t("admin.oidc.client-secret") }}:</strong> {{ $t("admin.oidc.help.google-example.client-secret-example") }}</li>
                  </ul>

                  <v-alert type="info" class="mt-3">
                    <strong>{{ $t("admin.oidc.help.google-example.redirect-note") }}</strong>
                    {{ $t("admin.oidc.help.google-example.redirect-desc") }}
                  </v-alert>
                </div>
              </v-expansion-panel-text>
            </v-expansion-panel>
          </v-expansion-panels>

          <div class="mt-4">
            <v-btn
              variant="outlined"
              color="primary"
              :href="$t('admin.oidc.help.docs-link')"
              target="_blank"
              prepend-icon="mdi-book-open-variant"
            >
              {{ $t("admin.oidc.help.view-docs") }}
            </v-btn>
          </div>
        </v-card-text>
      </v-card>

      <v-row>
        <v-col cols="6">
          <div class="font-weight-bold">
            {{ isEdit ? $t("admin.oidc.edit") : $t("admin.oidc.create") }}
            {{ $t("admin.oidc.provider") }}
          </div>
          <v-divider class="mt-2 mb-5" />

          <v-row>
            <v-col cols="12">
              <help-text-field
                v-model="provider.name"
                :label="$t('admin.oidc.name')"
                variant="outlined"
                density="compact"
                :rules="requiredRules"
                required
                :help="$t('admin.oidc.name-help')"
              />
            </v-col>
            <v-col cols="12">
              <help-text-field
                v-model="provider.issuer"
                :label="$t('admin.oidc.issuer-url')"
                variant="outlined"
                density="compact"
                :rules="urlRules"
                required
                :help="$t('admin.oidc.issuer-url-help')"
              />
            </v-col>
            <v-col cols="12">
              <help-text-field
                v-model="provider.clientId"
                :label="$t('admin.oidc.client-id')"
                variant="outlined"
                density="compact"
                :rules="requiredRules"
                required
                :help="$t('admin.oidc.client-id-help')"
              />
            </v-col>
            <v-col cols="12">
              <help-text-field
                v-model="provider.clientSecret"
                :label="$t('admin.oidc.client-secret')"
                variant="outlined"
                density="compact"
                :rules="requiredRules"
                required
                :help="$t('admin.oidc.client-secret-help')"
                :type="showSecret ? 'text' : 'password'"
                :append-inner-icon="showSecret ? 'mdi-eye-off' : 'mdi-eye'"
                @click:append-inner="showSecret = !showSecret"
              />
            </v-col>
            <v-col cols="12">
              <help-text-field
                v-model="fixedScopes"
                :label="$t('admin.oidc.fixed-scopes')"
                variant="outlined"
                density="compact"
                :help="$t('admin.oidc.fixed-scopes-help')"
                :placeholder="$t('admin.oidc.fixed-scopes-placeholder')"
                :disabled="true"
              />
            </v-col>
            <v-col cols="12">
              <help-text-field
                v-model="provider.scope"
                :label="$t('admin.oidc.optional-scopes')"
                variant="outlined"
                density="compact"
                required
                :help="$t('admin.oidc.optional-scopes-help')"
                :placeholder="$t('admin.oidc.optional-scopes-placeholder')"
              />
            </v-col>
            <v-col cols="6">
              <help-text-field
                v-model="provider.claimKey"
                :label="$t('admin.oidc.claim-key')"
                variant="outlined"
                density="compact"
                :help="$t('admin.oidc.claim-key-help')"
                :placeholder="$t('admin.oidc.claim-key-placeholder')"
              />
            </v-col>
            <v-col cols="6">
              <help-text-field
                v-model="provider.claimValue"
                :label="$t('admin.oidc.claim-value')"
                variant="outlined"
                density="compact"
                :help="$t('admin.oidc.claim-value-help')"
                :placeholder="$t('admin.oidc.claim-value-placeholder')"
              />
            </v-col>
            <v-col cols="12" v-if="isEdit">
              <div class="d-flex align-center">
                <help-text-field
                  v-model="redirectUrl"
                  :label="$t('admin.oidc.redirect-url')"
                  variant="outlined"
                  density="compact"
                  :disabled="true"
                  :help="$t('admin.oidc.redirect-url-help')"
                  class="flex-grow-1"
                />
                <v-btn
                  size="medium"
                  icon
                  class="mt-n5 ml-2"
                  variant="text"
                  color="primary"
                  @click="copyRedirectUrl"
                  :title="$t('admin.oidc.copy-to-clipboard')"
                >
                  <v-icon>mdi-content-copy</v-icon>
                </v-btn>
              </div>
            </v-col>
          </v-row>
          <v-btn
            color="primary"
            type="submit"
            :loading="submitting"
            class="mr-2"
            :disabled="!isValidProvider"
            @click="submit"
          >
            {{ isEdit ? $t("admin.oidc.update") : $t("admin.oidc.create") }}
          </v-btn>
          <v-btn variant="text" color="primary" @click="goBack">
            <span class="i18n" id="admin.cancel">{{ $t("admin.cancel") }}</span>
          </v-btn>
        </v-col>
      </v-row>
    </template>
  </page>
</template>

<script>
  import HelpTextField from "../../Base/HelpTextField.vue"

  export default {
    name: "CreateOIDCProvider",
    components: {
      HelpTextField
    },
    data() {
      return {
        submitting: false,
        showSecret: false,
        provider: {
          name: "",
          issuer: "",
          clientId: "",
          clientSecret: "",
          scope: "",
          claimKey: "",
          claimValue: ""
        },
        fixedScopes: "openid profile email",
        requiredRules: [(v) => !!v || this.$t("admin.oidc.field-required")],
        urlPattern:
          /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,
        loading: true
      }
    },
    computed: {
      isEdit() {
        return !!this.$route.params.id
      },
      breadcrumbs() {
        return [
          { text: this.$t("admin.admin"), disabled: true },
          {
            text: this.$t("admin.oidc.providers"),
            exact: true,
            to: { name: "OIDCProviders" }
          },
          {
            text: this.isEdit
              ? this.$t("admin.oidc.edit")
              : this.$t("admin.oidc.add"),
            disabled: true
          }
        ]
      },
      urlRules() {
        return [
          (v) => !!v || this.$t("admin.valid-url"),
          (v) => this.urlPattern.test(v) || this.$t("admin.valid-url")
        ]
      },
      isValidProvider() {
        // Check if all required fields are filled and valid
        const requiredFields = [
          this.provider.name,
          this.provider.issuer,
          this.provider.clientId,
          this.provider.clientSecret
        ]

        // Check if all required fields have values
        const allRequiredFieldsFilled = requiredFields.every((field) => !!field)

        // Check if issuer URL is valid
        const isIssuerUrlValid = this.urlPattern.test(this.provider.issuer)

        return allRequiredFieldsFilled && isIssuerUrlValid
      },
      redirectUrl() {
        // Construct the redirect URL
        if (!this.isEdit || !this.$route.params.id) return ""
        return `https://${this.iamDomain}/oidc/callback/${this.$route.params.id}`
      }
    },
    created() {
      if (this.isEdit) {
        this.loadProvider()
      }
    },
    mounted() {
      this.loading = false
    },
    methods: {
      loadProvider() {
        this.loading = true
        this.call(
          "getOIDCProvider",
          { providerId: this.$route.params.id },
          (data) => {
            // Make a copy of the provider data
            const providerData = { ...data }

            // If we have scopes and fixed scopes, remove the fixed scopes from the scope field
            if (providerData.scope && this.fixedScopes) {
              // Split the scopes into arrays
              const allScopes = providerData.scope.split(" ")
              const fixedScopesArray = this.fixedScopes.split(" ")

              // Filter out fixed scopes from all scopes
              const optionalScopes = allScopes.filter(
                (scope) => !fixedScopesArray.includes(scope)
              )

              // Set the provider scope to just the optional scopes
              providerData.scope = optionalScopes.join(" ")
            }

            this.provider = providerData
          },
          (err) => {
            this.showPopup("error", err.message)
            this.goBack()
          },
          () => {
            this.loading = false
          }
        )
      },
      submit() {
        if (!this.isValidProvider) {
          this.showPopup(
            "error",
            this.$t("vco.please-fill-all-required-fields")
          )
          return
        }

        this.submitting = true
        const method = this.isEdit ? "updateOIDCProvider" : "createOIDCProvider"
        const params = this.isEdit
          ? { providerId: this.$route.params.id, payload: this.provider }
          : { payload: this.provider }

        this.call(
          method,
          params,
          () => {
            this.showPopup(
              "success",
              this.$t("admin.oidc.provider-success", [
                this.isEdit
                  ? this.$t("admin.oidc.updated")
                  : this.$t("admin.oidc.created")
              ])
            )

            if (!this.isEdit) {
              this.$router.push({
                name: "CreateOIDCProvider",
                params: { id: this.provider.id }
              })
            }
            this.loadProvider()
          },
          (err) => {
            this.showPopup("error", err.message)
          },
          () => {
            this.submitting = false
          }
        )
      },
      goBack() {
        this.$router.push({ name: "OIDCProviders" })
      },
      copyRedirectUrl() {
        this.copy(this.redirectUrl)
        setTimeout(() => {
          this.showPopup("success", this.$t("admin.oidc.redirect-url-copied"))
        }, 10)
      }
    }
  }
</script>
