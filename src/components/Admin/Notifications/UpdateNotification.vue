<template>
  <page :loading="loading" id="notification">
    <template #content>
      <breadcrumbs :items="breadcrumbs"></breadcrumbs>
      <Help topicId="create-notification" :title="$t('admin.creating-notifications')" class="mb-9" style="width: 800px">
        <ul>
          <li>
            <p>
              <span class='i18n' id='admin.when-creating-a-new-planned-maintenance-notification'>{{ $t('admin.when-creating-a-new-planned-maintenance-notification') }}</span>
              <code>start_time</code> <span class='i18n' id='admin.and'>{{ $t('admin.and') }}</span> <code>end_time</code> <span class='i18n' id='admin.placeholders-in-the-content-will-be-replaced-with-dates-in-utc-timezone'>{{ $t('admin.placeholders-in-the-content-will-be-replaced-with-dates-in-utc-timezone') }}</span>
            </p>
          </li>
          <li>
            <p>
              <span class='i18n' id='admin.when-sending-planned-maintenance-notifications'>{{ $t('admin.when-sending-planned-maintenance-notifications') }}</span>
              <code>start_time</code> <span class='i18n' id='admin.and'>{{ $t('admin.and') }}</span> <code>end_time</code> {{ $t('admin.utc-dates-in-the-content-will-be-replaced-with-hostname-timezone', [hostname]) }}
              <span v-if="vcoTimezone">{{ $t('admin.vcotimezone', [vcoTimezone]) }}</span>
            </p>
          </li>
          <li>
            <p>
              <span class='i18n' id='admin.notifications-in-status'>{{ $t('admin.notifications-in-status') }}</span> <code>SENT</code> <span class='i18n' id='admin.can-not-be-updated-or-deleted'>{{ $t('admin.can-not-be-updated-or-deleted') }}</span>
            </p>
          </li>
          <li>
            <p>
              <span class='i18n' id='admin.total-size-of-images-attached-to-the-notification-content-is-limited-to-1mb'>{{ $t('admin.total-size-of-images-attached-to-the-notification-content-is-limited-to-1mb') }}</span>
            </p>
          </li>
          <li>
            <p><span class='i18n' id='admin.variables-available-in-the-notification-content-that-will-be-rendered-on-notification-sending-blog-publishing-and-blog-updating'>{{ $t('admin.variables-available-in-the-notification-content-that-will-be-rendered-on-notification-sending-blog-publishing-and-blog-updating') }}</span></p>
            <ul>
              <li><code v-pre>{{vco_name}}</code><span class='i18n' id='admin.the-vco-name'>{{ $t('admin.the-vco-name') }}</span></li>
              <li><code v-pre>{{vco_link}}</code><span class='i18n' id='admin.the-vco-link-as'>{{ $t('admin.the-vco-link-as') }}</span> <code>https://example.com</code> <span class='i18n' id='admin.and-can-be-used-while-adding-links'>{{ $t('admin.and-can-be-used-while-adding-links') }}</span></li>
              <li><code v-pre>{{vco_logo}}</code><span class='i18n' id='admin.the-vco-logo'>{{ $t('admin.the-vco-logo') }}</span></li>
            </ul>
            <p class="my-3"><span class='i18n' id='admin.variables-limited-to-planned-maintenance-and-outage-warnings'>{{ $t('admin.variables-limited-to-planned-maintenance-and-outage-warnings') }}</span></p>
            <ul>
              <li><code v-pre>{{start_date}}</code><span class='i18n' id='admin.the-notification-start-date-ex'>{{ $t('admin.the-notification-start-date-ex') }}</span><code><span class='i18n' id='admin.wednesday-22nd-of-march'>{{ $t('admin.wednesday-22nd-of-march') }}</span></code>
              </li>
              <li><code v-pre>{{start_time}}</code><span class='i18n' id='admin.the-notification-start-time-ex'>{{ $t('admin.the-notification-start-time-ex') }}</span><code><span class='i18n' id='admin.wednesday-22nd-of-march-10-39-am-utc-time'>{{ $t('admin.wednesday-22nd-of-march-10-39-am-utc-time') }}</span></code></li>
              <li><code v-pre>{{end_time}}</code><span class='i18n' id='admin.the-notification-end-time-ex'>{{ $t('admin.the-notification-end-time-ex') }}</span><code><span class='i18n' id='admin.wednesday-22nd-of-march-10-39-am-utc-time'>{{ $t('admin.wednesday-22nd-of-march-10-39-am-utc-time') }}</span></code></li>
              <li><code v-pre>{{location}}</code><span class='i18n' id='admin.the-affected-locations-as'>{{ $t('admin.the-affected-locations-as') }}</span> <code><span class='i18n' id='admin.city-location-name'>{{ $t('admin.city-location-name') }}</span></code></li>
              <li><code v-pre>{{reason}}</code><span class='i18n' id='admin.the-notification-reason'>{{ $t('admin.the-notification-reason') }}</span></li>
              <li><code v-pre>{{impact}}</code><span class='i18n' id='admin.the-notification-impact'>{{ $t('admin.the-notification-impact') }}</span></li>
              <li><code v-pre>{{status}}</code><span class='i18n' id='admin.the-notification-status'>{{ $t('admin.the-notification-status') }}</span></li>
            </ul>
            <p />
          </li>
        </ul>
      </Help>
      <v-form style="width: 800px" :disabled="false">
        <v-row v-if="notificationId">
          <v-col cols="6">
            <div class="font-weight-medium">
              <span class='i18n' id='admin.notification-status'>{{ $t('admin.notification-status') }}</span>
              <span class="font-weight-normal text-subtitle-1 ml-2 notification-status"
                :class="editMode ? 'text-red' : 'text-green'">
                {{ notificationStatus }}
              </span>
            </div>
          </v-col>
        </v-row>
        <v-row v-if="notificationId && pageText == 'Update'">
          <v-col cols="6">
            <div class="mb-1 mt-n5 font-weight-medium">
              <span class='i18n' id='admin.blog-status'>{{ $t('admin.blog-status') }}</span>
              <span class="font-weight-normal text-subtitle-1 ml-2 notification-status"
                :class="posted ? 'text-green' : 'text-red'">
                {{ posted? 'Published' : 'Not created' }}
              </span>
            </div>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="6">
            <v-text-field v-model="title"
variant="outlined"
density="compact"
required
:label="$t('admin.title')"
              :placeholder="$t('admin.notification-title')"
:disabled="!editMode"
id="notification-title" />
          </v-col>
          <v-col cols="6">
            <v-select id="notification-select"
:items="notificationTypes"
:disabled="pageText != 'Create'"
              ref="notificationType"
@click.stop
item-title="text"
item-value="value"
:label="$t('admin.notification-type')"
              v-model="notificationType"
density="compact"
variant="outlined"></v-select>
          </v-col>
        </v-row>
        <v-row>
          <div class="px-3 font-weight-light"><span class='i18n' id='admin.notification-content'>{{ $t('admin.notification-content') }}</span></div>
        </v-row>
        <v-row>
          <v-col cols="12" class="notification-content">
            <QuillEditor :content="content" @updateContent="updateContent" ref="contentEditor" />
          </v-col>
        </v-row>
        <template v-if="locationRelated">
          <v-row>
            <v-col cols="6">
              <v-autocomplete v-model="selectedLocations"
:items="locations"
id="notification-locations"
                ref="notificationLocation"
@click.stop="toggleLocationSelect = true"
                :menu-props="{ value: toggleLocationSelect }"
:label="$t('admin.locations')"
multiple
clearable
variant="outlined"
                density="compact"
required
hint="select affected locations"
:disabled="!editMode"></v-autocomplete>
            </v-col>
            <v-col cols="6">
              <v-select v-model="serviceImpact"
:items="serviceImpacts"
ref="notificationImpact"
                id="notification-impact"
@click.stop
item-title="text"
item-value="value"
:label="$t('admin.service-impact')"
                density="compact"
variant="outlined"
:disabled="!editMode"></v-select>
            </v-col>
          </v-row>
          <template v-if="notificationType == 'PLANNED_MAINTENANCE'">
            <v-row>
              <v-col cols="6">
                <v-select v-model="maintenanceStatus"
:items="maintenanceStatuses"
ref="notificationMaintenanceStatus"
                  id="notification-status"
@click.stop
item-title="text"
item-value="value"
:label="$t('admin.maintenance-status')"
                  density="compact"
variant="outlined"
:disabled="!editMode"></v-select>
              </v-col>
              <v-col cols="6">
                <v-text-field v-model="reason"
id="notification-reason"
variant="outlined"
density="compact"
required
                  :label="$t('admin.reason')"
:placeholder="$t('admin.notification-reason')"
:disabled="!editMode" />
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6">
                <DatePicker v-model.number="dates.from" :datePicker="datePicker" mode="dateTime">
                  <template v-slot="{ inputValue, inputEvents }">
                    <v-text-field :value="inputValue"
v-on="inputEvents"
:placeholder="$t('admin.from')"
id="notification-from"
                      variant="outlined"
density="compact"
prepend-inner-icon="mdi-calendar"
:disabled="!editMode" />
                  </template>
                </DatePicker>
              </v-col>
              <v-col cols="6">
                <DatePicker v-model.number="dates.till" :datePicker="datePicker" mode="dateTime">
                  <template v-slot="{ inputValue, inputEvents }">
                    <v-text-field :value="inputValue"
v-on="inputEvents"
:placeholder="$t('admin.till')"
id="notification-till"
                      variant="outlined"
density="compact"
prepend-inner-icon="mdi-calendar"
:disabled="!editMode" />
                  </template>
                </DatePicker>
              </v-col>
            </v-row>
          </template>
        </template>
        <v-row>
          <v-col cols="6">
            <v-btn raised
color="primary"
class="notification-save mr-4"
:disabled="submittingNotification"
              :loading="submittingNotification"
@click="submitNotification"
v-if="editMode"><span class='i18n' id='admin.save'>{{ $t('admin.save') }}</span></v-btn>
            <v-btn variant="text"
color="primary"
class="notification-cancel"
:disabled="submittingNotification"
              @click="close"><span class='i18n' id='admin.cancel'>{{ $t('admin.cancel') }}</span></v-btn>
          </v-col>
        </v-row>
      </v-form>
      <v-row>
        <v-col cols="auto">
          <v-dialog transition="dialog-bottom-transition" v-model="testEmailDialog" content-class="test-email-dialog">
            <template v-slot:default="{ isActive }">
              <v-card>
                <v-toolbar color="primary" dark class="px-4"><span class='i18n' id='admin.test-email-notification'>{{ $t('admin.test-email-notification') }}</span> <v-spacer />
                  <v-btn variant="text" size="medium" elevation="0" @click="isActive.value = false"><v-icon>mdi-close</v-icon></v-btn>
                </v-toolbar>
                <v-card-title class="text-h6 lighten-2 mb-2 d-flex">
                  <span class='i18n' id='admin.send-the-email-notification-to-the-following-addresses'>{{ $t('admin.send-the-email-notification-to-the-following-addresses') }}</span>
                </v-card-title>
                <v-col cols="10" style="margin:0 auto;">
                  <v-chip close-icon="mdi-close"
@click:close="removeTestEmail(email)"
closable
variant="outlined"
                    class="mr-3 mb-5 text-grey-darken-3"
v-for="email in emails"
:key="email">
                    {{ email }}
                  </v-chip>
                  <v-text-field v-model="email"
variant="outlined"
density="compact"
required
:label="$t('admin.add-email')"
                    :placeholder="$t('admin.add-email')"
@keyup.enter="addTestEmail"
id="notification-preview-input">
                    <template v-slot:append-inner>
                      <v-icon class="text-green-darken-3"
style="cursor:pointer;"
@click="addTestEmail"
                        id="notification-preview-add">
                        mdi-plus
                      </v-icon>
                    </template>
                  </v-text-field>
                </v-col>
                <v-card-actions class="justify-end">
                  <v-btn variant="text" id="notification-preview-cancel" @click="isActive.value = false"><span class='i18n' id='admin.close'>{{ $t('admin.close') }}</span></v-btn>
                  <v-btn variant="text"
class="bg-primary text-white"
id="notification-preview-send"
                    @click="sendTestNotification"><span class='i18n' id='admin.send'>{{ $t('admin.send') }}</span></v-btn>
                </v-card-actions>
              </v-card>
            </template>
          </v-dialog>
        </v-col>
      </v-row>
    </template>
  </page>
</template>

<script>
import Page from "../../Base/Page.vue"
import Help from "../../Base/Help.vue"
import QuillEditor from './QuillEditor.vue'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css' 
import 'quill/dist/quill.bubble.css' 
import { DatePicker } from 'v-calendar';
import 'v-calendar/style.css';

export default {
  components: { Page, Help, QuillEditor, DatePicker },
  name: "UpdateNotification",
  data() {
    return {
      loading: false,
      submittingNotification: false,
      notificationStatus: "",
      title: "",
      notificationTypes: [],
      locationNotifications: [],
      notificationType: "",
      locationRelated: false,
      posted: false,
      content: "",
      locations: [],
      toggleLocationSelect: false,
      selectedLocations: [],
      reason: "",
      serviceImpacts: [],
      serviceImpact: "LIMITED_IMPACT",
      maintenanceStatuses: [],
      maintenanceStatus: "PLANNED",
      notification: {},
      dates: {
        from: "",
        till: "",
      },
      deleting: false,
      sending: false,
      sendingTest: false,
      copying: false,
      creatingBlog: false,
      unpublishing: false,
      previewUpdate: false,
      notificationRefs: [
        "notificationType",
        "notificationLocation",
        "notificationImpact",
        "notificationMaintenanceStatus",
      ],
      testEmailDialog: false,
      emails: [],
      email: "",
      vcoTimezone: null,
      datePicker: {
        popover: {
          visibility: "click",
        },
      },
      apiNames: {
        notification: {
          get: 'getVCONotification',
          delete: 'deleteVCONotification',
          update: 'updateVCONotification'
        },
        blog: {
          get: 'getNotificationBlog',
          delete: 'deleteNotificationBlog',
          update: 'updateNotificationBlog'
        }
      }
    }
  },
  computed: {
    breadcrumbs() {
      return [
        { text: this.$t('admin.admin'), disabled: true },
        {
          text: this.$t('admin.notifications'),
          disabled: false,
          exact: true,
          to: { name: "Notifications" },
        },
        { text: this.$route.meta.bcText, disabled: true },
      ]
    },
    subtop() {
      return [
        {
          btn: {
            text: this.$t('admin.send'),
            action: "sendNotification",
            condition: this.pageText == "Update" && !this.blogId,
            loading: this.sending,
          },
          icon: {
            text: "mdi-email",
          },
        },
        {
          btn: {
            text: this.$t('admin.send-preview'),
            action: "openEmailDialog",
            condition: this.pageText == "Update" && !this.blogId,
            loading: this.sendingTest,
          },
          icon: {
            text: "mdi-email",
          },
        },
        {
          btn: {
            text: this.$t('admin.copy'),
            action: "copyNotification",
            condition: this.pageText == "Update" && !this.blogId,
            loading: this.copying,
            attrs: {},
          },
          icon: {
            text: "mdi-content-copy",
          },
        },
        {
          btn: {
            text: this.$t('admin.create-blog'),
            action: "createBlog",
            condition: this.pageText == "Update" && !!this.notificationId && !this.posted,
            loading: this.creatingBlog,
            attrs: {},
          },
          icon: {
            text: "mdi-plus",
          },
        },
        {
          btn: {
            text: this.$t('admin.unpublish-blog'),
            action: "unpublishBlog",
            condition: this.pageText == "Update" && !!this.notificationId && this.posted,
            loading: this.unpublishing,
            attrs: {},
          },
          icon: {
            text: "mdi-keyboard-return",
          },
        },
        {
          btn: {
            text: this.$t('admin.blog-preview'),
            condition: this.pageText == "Update" && !!this.notificationId,
            action: 'goToPreviewPage',
            loading: this.previewUpdate,
            attrs: {},
          },
          icon: {
            text: "mdi-eye",
          },
        },
        {
          btn: {
            text: this.$t('admin.delete'),
            action: "deleteResource",
            condition: this.pageText == "Update" && this.editMode,
            loading: this.deleting,
          },
          icon: {
            text: "mdi-delete",
          },
        },
      ]
    },
    notificationId() {
      return this.$route.params.notificationId
    },
    blogId() {
      return this.$route.params.blogId
    },
    pageText() {
      return this.$route.meta.bcText
    },
    hostname: () => {
      return window.location.hostname
    },
    editMode() {
      if (this.notificationId) {
        return this.notificationStatus != "SENT"
      }
      return true
    },
  },
  watch: {
    notificationType(val) {
      this.locationRelated = false
      if (this.locationNotifications.includes(val)) {
        this.locationRelated = true
      }
      if (this.$refs.contentEditor) { 
        if (val == "PLANNED_MAINTENANCE" && this.pageText == "Create") {
          this.$refs.contentEditor.updateEditor(`Dear customer,<br><br>On {{ start_date }} we have to perform a maintenance window on our cloud location in {{ location }}.<br>Reason: {{ reason }}<br>Service impact: {{ impact }}<br>Maintenance status: {{ status }}<br>Maintenance window start: {{ start_time }}<br>Maintenance window end: {{ end_time }}<br>`)
        } else if (val == "OUTAGE_WARNING" && this.pageText == "Create") {
          this.$refs.contentEditor.updateEditor(`Dear customer,<br><br>We are having an outage on our cloud location in {{ location }}.<br>`)
        } else {
          this.$refs.contentEditor.updateEditor('')
        }
      }
    },
    "$route.params.bcText": {
      handler: function() {
        this.getEnums()
        this.getLocations()
        this.$forceUpdate()
      },
      deep: true,
    },
  },
  created() {
    this.getEnums()
    this.getLocations()
    this.emails.unshift(this.userInfo.email)
  },
  mounted() {
    window.addEventListener("click", () => {
      this.toggleLocationSelect = false
      Object.values(this.notificationRefs).forEach((val) => {
        if (this.$refs[val]) {
          this.$refs[val].blur()
        }
      })
    })
  },
  methods: {
    getEnums() {
      this.loading = true
      this.call(
        "listNotificationUtilities",
        {},
        (data) => {
          this.locationNotifications = data.notification_types.locationNotification.map(
            (notify) => {
              return notify.name
            }
          )
          data.notification_types.locationNotification.forEach((e) => {
            let obj = {
              text: this.enumToText(e.name),
              value: e.name,
            }
            this.notificationTypes.push(obj)
          })
          data.notification_types.nonLocationNotification.forEach((e) => {
            let obj = {
              text: this.enumToText(e.name),
              value: e.name,
            }
            this.notificationTypes.push(obj)
          })
          data.statuses.forEach((e) => {
            let obj = {
              text: this.enumToText(e.name),
              value: e.name,
            }
            this.maintenanceStatuses.push(obj)
          })
          data.impacts.forEach((e) => {
            let obj = {
              text: this.enumToText(e.name),
              value: e.name,
            }
            this.serviceImpacts.push(obj)
          })
        },
        (err) => {
          this.showPopup("error", err.message)
        },
        () => {
          if (this.notificationId || this.blogId) {
            this.getNotification()
          } else {
            this.notificationType = 'NEWS_AND_UPDATES'
            this.loading = false
          }
        }
      )
      this.call(
        "getVCOTimezone",
        {},
        (data) => {
          this.vcoTimezone = data.timezone
        },
        (err) => {
          console.log(err.message)
        }
      )
    },
    getLocations() {
      this.call(
        "listLocations",
        {},
        (data) => {
          data.result.forEach((location) => {
            this.locations.push(location.name)
          })
        },
        (err) => {
          this.showPopup("error", err.message)
        }
      )
    },
    textToEnum(txt) {
      if (!txt) return undefined
      txt = txt.toUpperCase()
      txt = txt.split(" ").join("_")
      return txt
    },
    getNotification() {
      this.loading = true
      let params = {}
      if (this.notificationId) {
        params = { notificationId: this.notificationId }
      } else if (this.blogId) {
        params = { blogId: this.blogId }
      }
      this.call(
        this.getAPiName('get'),
        params,
        (data) => {
          this.notification = data
          this.title = data.title
          this.content = data.content
          this.notificationStatus = data.status
          this.notificationType = data.notification_type
          this.posted = data.posted
          if (this.pageText == "Copy") {
            this.notificationStatus = "DRAFT"
            this.notification.status = "DRAFT"
          }
          if (this.locationNotifications.includes(data.notification_type)) {
            setTimeout(() => {
              this.locationRelated = true
            }, 10)
            this.selectedLocations = data.locations.map((loc) => loc.name)
            this.serviceImpact = data.service_impact
            if (this.notificationType == "PLANNED_MAINTENANCE") {
              this.reason = data.reason
              this.maintenanceStatus = data.maintenance_status
              this.dates.from = data.from_time * 1000
              this.dates.till = data.till_time * 1000
            }
          }
        },
        (err) => {
          this.showPopup("error", err.message)
        },
        () => {
          this.loading = false
        }
      )
    },
    submitNotification() {
      return new Promise((resolve, reject) => {
        this.submittingNotification = true
        let notificationData = {
          notification_type: this.notificationType,
          title: this.title,
          content: this.content,
        }
        if (this.locationNotifications.includes(this.notificationType)) {
          let locationData = {
            locations: this.selectedLocations.map((loc) => {
              return { name: loc }
            }),
            service_impact: this.serviceImpact,
          }
          if (this.notificationType == "PLANNED_MAINTENANCE") {
            locationData.from_time = this.dates.from / 1000
            locationData.till_time = this.dates.till / 1000
            locationData.maintenance_status = this.maintenanceStatus
            locationData.reason = this.reason
          }
          notificationData = Object.assign(notificationData, locationData)
        }
        if (this.pageText == "Update") {
          let params = {}
          if (this.notificationId) {
            params = { notificationId: this.notificationId, payload: notificationData }
          } else if (this.blogId) {
            params = { blogId: this.blogId, payload: notificationData }
          }
          this.call(
            this.getAPiName('update'),
            params,
            () => {
              this.showPopup("success", this.$t('admin.notification-successfully-updated'))
              resolve()
            },
            (err) => {
              if (err.response.status == 413) {
                let msg =
                  this.$t('admin.images-attached-to-the-notification-are-too-large-limit-is-1mb')
                this.showPopup("error", msg)
              } else {
                this.showPopup("error", err.message)
              }
              reject()
            },
            () => {
              this.submittingNotification = false
            }
          )
        } else {
          this.call(
            "createVCONotification",
            { payload: notificationData },
            (notification_id) => {
              let successText = this.pageText == "Copy" ? this.$t('admin.notification-successfully-copied') : this.$t('admin.notification-successfully-created')
              this.$router.push(
                {
                  name: "UpdateNotification",
                  params: { notificationId: notification_id },
                }
              )
              this.showPopup("success", successText)
              this.getNotification()
              resolve()
            },
            (err) => {
              if (err.response.status == 413) {
                let msg =
                  this.$t('admin.images-attached-to-the-notification-are-too-large-limit-is-1mb')
                this.showPopup("error", msg)
              } else {
                this.showPopup("error", err.message)
              }
              reject()
            },
            () => {
              this.submittingNotification = false
            }
          )
        }
      })
    },
    deleteResource() {
      if (this.notificationId) {
        this.deleteNotification()
      } else if (this.blogId) {
        this.deleteBlog()
      }
    },
    deleteNotification() {
      this.confirm(
        this.$t('admin.delete-notification'),
        (this.$t('admin.please-confirm-deleting-notification') + this.title),
        () => {
          this.deleting = true
          this.call(
            this.getAPiName('delete'),
            { notificationId: this.notificationId },
            () => {
              this.$router.push({ name: "Notifications" })
              this.showPopup("success", this.$t('admin.notification-deleted-successfully'))
            },
            (err) => {
              this.showPopup("error", err.message)
              this.deleting = false
            }
          )
        },
        false
      )
    },
    deleteBlog() {
      this.confirm(
        this.$t('admin.delete-notification-blog'),
        (this.$t('admin.please-confirm-deleting-notification-blog') + this.title),
        () => {
          this.deleting = true
          this.call(
            this.getAPiName('delete'),
            { blogId: this.blogId },
            () => {
              this.$router.push({ name: "CustomerLanding", params: { customerId: this.activeCustomer } }, () => {
                this.showPopup("success", this.$t('admin.notification-blog-deleted-successfully'))
              })
            },
            (err) => {
              this.showPopup("error", err.message)
              this.deleting = false
            }
          )
        },
        false
      )
    },
    sendNotification() {
      let msg = this.$t('admin.publish-this-notification-and-send-it-to-all-subscribed-users')
      if (this.notificationStatus == "SENT") {
        msg =
          this.$t('admin.this-notification-is-already-published-and-sent-to-its-recipients-send-again-to-all-subscribed-users')
      }
      this.confirm(
        this.$t('admin.send-notification'),
        msg,
        () => {
          this.sending = true
          this.call(
            "sendVCONotification",
            { notificationId: this.notificationId },
            () => {
              setTimeout(() => {
                this.showPopup("success", this.$t('admin.notification-sent-successfully'))
              }, 1000)
              this.notificationStatus = "SENT"
            },
            (err) => {
              this.showPopup("error", err.message)
            },
            () => {
              this.sending = false
            }
          )
        },
        false
      )
    },
    sendTestNotification() {
      this.sendingTest = true
      this.testEmailDialog = false
      if(this.email) {
        this.emails.push(this.email)
        this.email = ''
      }
      let success_text = this.$t('admin.test-notification-is-sent-to-your-emails')
      if (this.notificationStatus == "SENT") {
        setTimeout(() => {
          this.showPopup("info", this.$t('admin.notification-already-sent'))
          success_text = this.$t('admin.test-notification-is-sent-to-your-emails')
        }, 10)
      }
      this.call(
        "sendVCOTestNotification",
        {
          notificationId: this.notificationId,
          payload: {
            emails: this.emails.map((e) => {
              return { email: e }
            }),
          },
        },
        () => {
          setTimeout(() => {
            this.showPopup("success", success_text)
          }, 1000)
        },
        (err) => {
          this.showPopup("error", err.message)
        },
        () => {
          this.sendingTest = false
        }
      )
    },
    copyNotification() {
      this.$router.push(
        {
          name: "CopyNotification",
          params: { notificationId: this.notificationId },
        },
        () => {
          this.getNotification()
        }
      )
    },
    createBlog() {
      this.confirm(this.$t('admin.create-blog'), this.$t('admin.please-confirm-publishing-this-blog'), async () => {
        this.creatingBlog = true
        let update = this.content != this.notification.content
        if(this.editMode && update){
          setTimeout(() => this.showPopup("info", this.$t('admin.updating-notification')), 1)
          await this.submitNotification()
        }
        this.call("createNotificationBlog", { notificationId: this.notificationId }, () => {
          this.showPopup("success", this.$t('admin.blog-created-successfully'))
          this.posted = true
        }, (error) => this.showPopup("error", error.message), () => (this.creatingBlog = false))
      }, false)
    },
    unpublishBlog() {
      this.confirm(
        this.$t('admin.unpublish-blog'),
        `Please confirm unpublishing this blog`,
        () => {
          this.unpublishing = true
          this.call("unpublishVCONotification", { notificationId: this.notificationId }, () => {
            this.showPopup('success', this.$t('admin.blog-unpublished-successfully'))
            this.posted = false
          }, error => {
            this.showPopup('error', error.message)
          }, () => {
            this.unpublishing = false
          })
        }, false)
    },
    close() {
      if (this.blogId) {
        this.$router.push({ name: "CustomerLanding", params: { customerId: this.activeCustomer } })
      } else {
        this.$router.push({ name: "Notifications" })
      }
    },
    openEmailDialog() {
      this.testEmailDialog = true
    },
    removeTestEmail(email) {
      this.emails = this.emails.filter((e) => e != email)
    },
    addTestEmail() {
      if (this.email) {
        this.emails.push(this.email)
        this.email = ""
      }
    },
    getAPiName(name){
      if (this.notificationId) {
        return this.apiNames.notification[name]
      }
      return this.apiNames.blog[name]
    },
    updateContent(val) {
      this.content = val
    },
    async goToPreviewPage(){
      let update = this.content != this.notification.content
      if(!this.editMode || !update){
        this.$router.push({name: 'BlogPreview', params: {notificationId: this.notificationId}})
        return
      }
      this.previewUpdate = true
      setTimeout(() => this.showPopup("info", this.$t('admin.updating-notification')), 1)
      await this.submitNotification()
      this.previewUpdate = false
      this.$router.push({name: 'BlogPreview', params: {notificationId: this.notificationId}})
    }
  },
}
</script>

<style lang="scss">
#notification {
  .notification-content {
    display: flex;
    flex-direction: column;

    #content-editor {
      margin-bottom: 20px;
    }

    .ql-container {
      resize: vertical;
      overflow: auto;
      height: 250px;

      .ql-tooltip.ql-editing{
        transform: translateX(149px);
      }
    }
    .disabled {
      color: rgba(0, 0, 0, 0.38);
    }
  }
}
.test-email-dialog {
  max-width: 650px !important;
  max-height: 500px !important;

  .v-toolbar__content {
    font-size: 20px !important;
  }

  .v-label {
    flex-direction: column;
    align-items: baseline !important;
  }

  &::-webkit-scrollbar {
    width: 10px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px 4px #00000054;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #3d81cfe8;
    outline: 1px solid #3a7cc7;
  }

  .v-chip {
    text-transform: none !important;
  }
}
.v-time-picker-title__time > div {
  font-size: 70px !important;
}
.v-picker__title {
  border-radius: 0 !important;
}
</style>
