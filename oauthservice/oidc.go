package oauthservice

import (
	"crypto/ecdsa"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"git.gig.tech/gig-meneja/iam/db/user"
	jwt "github.com/dgrijalva/jwt-go"
	log "github.com/sirupsen/logrus"
)

const (
	// scopeOpenID is the mandatory scope for all OpenID Connect OAuth2 requests.
	scopeOpenID      = "openid"
	responseTypeCode = "code"
	signingAlgES384  = "ES384"
)

// getIDTokenFromCode returns an ID token if scopes associated with the code match
// the OpenId scope
// If no openId scope is found, the returned string is empty
// if no error, the int returned represents http.StatusOK
func getIDTokenFromCode(code string, jwtSigningKey *ecdsa.PrivateKey, r *http.Request, at *AccessToken, mgr *Manager, issuer string) (string, int) {
	// get scopes
	fmt.Println(at.Scope)
	ar, err := mgr.getAuthorizationRequest(code)
	if err != nil {
		log.Debugf("something went wrong getting authorize request for the ID token: %s", err)
		return "", http.StatusInternalServerError
	}
	scopeStr := ar.Scope

	if !scopePresent(scopeStr, scopeOpenID) {
		return "", http.StatusOK
	}

	token, err := getIDTokenStr(jwtSigningKey, r, at, ar, issuer)
	if err != nil {
		log.Debugf("something went wrong getting ID token: %s", err)
		return "", http.StatusBadRequest
	}

	return token, http.StatusOK
}

// scopePresent returns true if scope is in the scope string
// The scope string is expected to be a comma seperated list of scopes
func scopePresent(scopeStr string, scopeToSearch string) bool {
	scopeSlice := strings.Split(scopeStr, ",")
	for _, scope := range scopeSlice {
		if scope == scopeToSearch {
			return true
		}
	}

	return false
}

// getIDTokenStr returns an oidc ID token string
// It will set the default required claims
// and calls setValuesFromScope to set additional claims
func getIDTokenStr(jwtSigningKey *ecdsa.PrivateKey, r *http.Request, at *AccessToken, ar *authorizationRequest, issuer string) (string, error) {
	token := jwt.New(jwt.SigningMethodES384)

	// setup basic claims
	token.Claims["sub"] = at.Username
	token.Claims["iat"] = at.CreatedAt.Unix()
	token.Claims["exp"] = at.ExpirationTime().Unix()
	token.Claims["aud"] = at.ClientID

	// Get Zammad configuration
	zammadClientID, _, _ := getZammadConfig()
	if at.ClientID == zammadClientID {
		token.Claims["iss"] = "https://" + issuer
	} else {
		token.Claims["iss"] = issuer
	}

	// Add nonce if present
	if ar.Nonce != "" {
		token.Claims["nonce"] = ar.Nonce
	}

	// check scopes for additional claims
	err := setValuesFromScope(token, ar.Scope, r, at)
	if err != nil {
		return "", fmt.Errorf("failed to get additional claims for id token: %s", err)
	}

	return token.SignedString(jwtSigningKey)
}

// setValuesFromScope check the scopes for additional claims to be added to the provided token
func setValuesFromScope(token *jwt.Token, scopeStr string, r *http.Request, at *AccessToken) error {
	userMgr := user.NewManager(r)
	authorization, err := userMgr.GetAuthorization(at.Username, at.ClientID)
	if err != nil {
		return fmt.Errorf("failed to get authorization: %s", err)
	}
	userObj, err := userMgr.GetByName(at.Username)
	if err != nil {
		return fmt.Errorf("failed to get user: %s", err)
	}

	scopeSlice := strings.Split(scopeStr, ",")
	for _, scope := range scopeSlice {
		switch {
		case scope == "user:name":
			token.Claims[scope] = fmt.Sprintf("%s %s", userObj.Firstname, userObj.Lastname)
		case strings.HasPrefix(scope, "user:email"):
			requestedLabel := strings.TrimPrefix(scope, "user:email")
			if requestedLabel == "" || requestedLabel == "user:email" {
				requestedLabel = "main"
			}
			label := getRealLabel(requestedLabel, "email", authorization)
			email, err := userObj.GetEmailAddressByLabel(label)
			if err != nil {
				return fmt.Errorf("could not get user's email: %s", err)
			}
			token.Claims[scope] = email.EmailAddress
		case strings.HasPrefix(scope, "user:validated:email"):
			requestedLabel := strings.TrimPrefix(scope, "user:validated:email")
			if requestedLabel == "" || requestedLabel == "user:validated:email" {
				requestedLabel = "main"
			}
			label := getRealLabel(requestedLabel, "validatedemail", authorization)
			email, err := userObj.GetEmailAddressByLabel(label)
			if err != nil {
				return fmt.Errorf("could not get user's email: %s", err)
			}
			token.Claims[scope] = email.EmailAddress
		case strings.HasPrefix(scope, "user:phone"):
			requestedLabel := strings.TrimPrefix(scope, "user:phone:")
			if requestedLabel == "" || requestedLabel == "user:phone" {
				requestedLabel = "main"
			}
			label := getRealLabel(requestedLabel, "phone", authorization)
			phone, err := userObj.GetPhonenumberByLabel(label)
			if err != nil {
				return fmt.Errorf("could not get user's phone: %s", err)
			}
			token.Claims[scope] = phone.Phonenumber
		case strings.HasPrefix(scope, "user:validated:phone"):
			requestedLabel := strings.TrimPrefix(scope, "user:validated:phone:")
			if requestedLabel == "" || requestedLabel == "user:validated:phone" {
				requestedLabel = "main"
			}
			label := getRealLabel(requestedLabel, "validatedphone", authorization)
			phone, err := userObj.GetPhonenumberByLabel(label)
			if err != nil {
				return fmt.Errorf("could not get user's phone: %s", err)
			}
			token.Claims[scope] = phone.Phonenumber
		}
	}

	return nil
}

// OpenIDConfiguration represents the OpenID Provider configuration response
type OpenIDConfiguration struct {
	Issuer                           string   `json:"issuer"`
	AuthorizationEndpoint            string   `json:"authorization_endpoint"`
	TokenEndpoint                    string   `json:"token_endpoint"`
	JwksURI                          string   `json:"jwks_uri"`
	UserinfoEndpoint                 string   `json:"userinfo_endpoint"`
	ScopesSupported                  []string `json:"scopes_supported"`
	ResponseTypesSupported           []string `json:"response_types_supported"`
	SubjectTypesSupported            []string `json:"subject_types_supported"`
	IDTokenSigningAlgValuesSupported []string `json:"id_token_signing_alg_values_supported"`
	ClaimsSupported                  []string `json:"claims_supported"`
}

// HandleOpenIDConfiguration handles the OpenID Connect discovery endpoint
func (service *Service) HandleOpenIDConfiguration(w http.ResponseWriter, r *http.Request) {

	// TODO-S: Remove this
	iss := "9924-197-53-54-63.ngrok-free.app"

	config := OpenIDConfiguration{
		// Issuer:                "https://" + service.issuer,
		// AuthorizationEndpoint: "https://" + service.issuer + "/v1/oauth/authorize",
		// TokenEndpoint:         "https://" + service.issuer + "/v1/oauth/access_token",
		// JwksURI:               "https://" + service.issuer + "/.well-known/service.jwtSigningKeys.json",
		// UserinfoEndpoint:      "https://" + service.issuer + "/v1/oauth/userinfo",
		Issuer:                "https://" + iss,
		AuthorizationEndpoint: "https://" + iss + "/v1/oauth/authorize",
		TokenEndpoint:         "https://" + iss + "/v1/oauth/access_token",
		JwksURI:               "https://" + iss + "/.well-known/service.jwtSigningKeys.json",
		UserinfoEndpoint:      "https://" + iss + "/v1/oauth/userinfo",
		ScopesSupported: []string{
			"openid",
		},
		ResponseTypesSupported: []string{
			responseTypeCode,
		},
		SubjectTypesSupported: []string{
			"public",
		},
		IDTokenSigningAlgValuesSupported: []string{
			signingAlgES384,
		},
		ClaimsSupported: []string{
			"sub",
			"iss",
			"iat",
			"exp",
			"aud",
			"user:name",
			"user:email",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(config)
}

// JWK represents a JSON Web Key
type JWK struct {
	Kty string `json:"kty"` // Key type
	Kid string `json:"kid"` // Key ID
	Use string `json:"use"` // Use ("sig" - signature)
	Crv string `json:"crv"` // Curve (P-384)
	X   string `json:"x"`   // X coordinate
	Y   string `json:"y"`   // Y coordinate
	Alg string `json:"alg"` // Algorithm (ES384)
}

// JWKS represents a JWK Set
type JWKS struct {
	Keys []JWK `json:"keys"`
}

func generateKeyID(publicKey *ecdsa.PublicKey) string {
	// Concatenate X and Y coordinates
	keyBytes := append(publicKey.X.Bytes(), publicKey.Y.Bytes()...)

	// Generate SHA-256 hash
	hash := sha256.Sum256(keyBytes)

	// Return base64url encoded first 8 bytes of hash
	return base64.RawURLEncoding.EncodeToString(hash[:8])
}

// Modify the HandleJWKS function
func (service *Service) HandleJWKS(w http.ResponseWriter, r *http.Request) {
	publicKey := &service.jwtSigningKey.PublicKey

	// Generate key ID based on public key components
	keyID := generateKeyID(publicKey)

	x := base64.RawURLEncoding.EncodeToString(publicKey.X.Bytes())
	y := base64.RawURLEncoding.EncodeToString(publicKey.Y.Bytes())

	jwk := JWK{
		Kty: "EC",
		Kid: keyID, // Use the generated key ID
		Use: "sig",
		Crv: "P-384",
		X:   x,
		Y:   y,
		Alg: signingAlgES384,
	}

	// Create the JWKS
	jwks := JWKS{
		Keys: []JWK{jwk},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(jwks)
}

// UserInfo represents the user information response
type UserInfo struct {
	Subject string `json:"sub"`
	Name    string `json:"name,omitempty"`
	Email   string `json:"email,omitempty"`
}

// HandleUserInfo handles the user information endpoint
func (service *Service) HandleUserInfo(w http.ResponseWriter, r *http.Request) {
	// Get bearer token from Authorization header
	authHeader := r.Header.Get("Authorization")
	if !strings.HasPrefix(authHeader, "Bearer ") {
		http.Error(w, "Invalid authorization header", http.StatusUnauthorized)
		return
	}
	token := strings.TrimPrefix(authHeader, "Bearer ")

	// Validate access token
	at, err := service.ValidateAccessToken(r, token)
	if err != nil {
		http.Error(w, "Invalid token", http.StatusUnauthorized)
		return
	}
	userMgr := user.NewManager(r)

	userObj, err := userMgr.GetByName(at.Username)

	label := "main"
	email, err := userObj.GetEmailAddressByLabel(label)
	if err != nil {
		fmt.Errorf("could not get user's email: %s", err)
		return
	}

	// Get user info
	userInfo := UserInfo{
		Subject: at.Username,
		Email:   email.EmailAddress, // Assuming username is email
		Name:    at.Username,        // You might want to fetch actual name from your user service
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(userInfo)
}

// ValidateAccessToken validates an access token and returns the corresponding AccessToken struct if valid
func (service *Service) ValidateAccessToken(r *http.Request, tokenString string) (*AccessToken, error) {
	// Get the token from database
	mgr := NewManager(r)
	accessToken, err := mgr.GetAccessToken(tokenString)
	if err != nil {
		return nil, err
	}

	// Check if token exists
	if accessToken == nil {
		return nil, errors.New("invalid token")
	}

	// Check if token is expired
	if accessToken.IsExpired() {
		return nil, errors.New("token expired")
	}

	return accessToken, nil
}
